# Project Madara - Automated Script Deployment System

## Overview

This guide covers the automated script deployment and loading system that allows you to easily deploy game-specific Roblox scripts with enterprise-grade security.

## 🚀 Quick Start

### 1. Add Your Scripts
Create `.lua` files in the `Roblox_Script/` directory named with their PlaceID:

```
<PERSON>lox_Script/
├── 18441747385.lua    # Custom game script
├── 142823291.lua      # Murder Mystery 2 script
├── 537413528.lua      # Build A Boat script
└── README.md          # Documentation
```

### 2. Deploy
Run the deployment command:
```bash
npm run deploy-scripts
```

Or deploy to production:
```bash
netlify deploy --prod
```

### 3. Test
Test your deployment:
```bash
npm run test-deployment
```

## 📁 File Structure

```
project-madara/
├── Roblox_Script/              # Your game scripts
│   ├── 18441747385.lua        # PlaceID-named scripts
│   └── README.md              # Instructions
├── scripts/
│   ├── auto-deploy-scripts.js # Core deployment logic
│   ├── deploy-integration.js  # Build integration
│   ├── test-deployment-system.js # Testing suite
│   ├── encrypt-scripts.js     # Encryption utilities
│   └── secure-script-manager.js # Script management
├── enhanced-main-loader.lua   # Enhanced secure loader
└── netlify/functions/
    └── secure-script.js       # Secure API endpoint
```

## 🔄 Deployment Flow

### Automatic Process
1. **Script Detection**: System scans `Roblox_Script/` for `.lua` files
2. **PlaceID Extraction**: Extracts PlaceID from filename (e.g., `18441747385.lua` → PlaceID: 18441747385)
3. **Encryption**: Encrypts script content using AES-256-GCM
4. **Database Storage**: Stores encrypted script in secure database
5. **Game Registry Update**: Updates supported games list
6. **Deployment Report**: Generates success/failure report

### Manual Commands
```bash
# Deploy scripts manually
npm run deploy-scripts

# Test deployment system
npm run test-deployment

# Encrypt existing scripts
npm run encrypt-scripts

# Manage scripts in database
npm run manage-scripts
```

## 🎮 Loader Logic Flow

### 1. Game Support Check
```lua
-- Enhanced loader checks if current game is supported
local gameSupported, gameInfo = checkGameSupport()
if not gameSupported then
    print("❌ Game not supported")
    return
end
```

### 2. Authentication State Management
```lua
-- Check for cached license key
local storedKey = getStoredKey()
if storedKey then
    -- Validate cached key
    local keyValid, result = validateKey(storedKey)
    if keyValid then
        -- Proceed to script loading
        loadGameScript(storedKey)
    else
        -- Clear invalid key and request new one
        clearStoredKey()
        showKeySystemUI()
    end
else
    -- No cached key, show key system UI
    showKeySystemUI()
end
```

### 3. Script Loading
```lua
-- Load game-specific script securely
local success, result = loadGameScript(validatedKey)
if success then
    print("🎉 Script loaded successfully!")
else
    print("❌ Script loading failed: " .. result)
end
```

## 🔒 Security Features

### Encryption
- **Algorithm**: AES-256-GCM with authenticated encryption
- **Key Derivation**: PBKDF2 with 100,000 iterations
- **Salt**: 32-byte random salt per script
- **IV**: 16-byte random initialization vector

### Obfuscation
- Variable name randomization
- Control flow obfuscation
- Anti-debugging measures
- String encoding

### Authentication
- License key validation with HWID binding
- Session-based access tokens
- Multi-layered security checks
- Comprehensive audit logging

## 📊 API Endpoints

### Game Support Check
```
GET /.netlify/functions/secure-script?action=check_game_support&place_id=18441747385
```

Response:
```json
{
  "supported": true,
  "game_name": "Custom Game",
  "place_id": "18441747385",
  "script_id": "abc123"
}
```

### Key System UI
```
GET /.netlify/functions/secure-script?action=get_key_system_ui
```

Returns Lua script for key input interface.

### Script Loading
```
POST /.netlify/functions/secure-script
Content-Type: application/json
X-Key-Code: MADARA-XXXX-XXXX-XXXX
X-HWID: user-hardware-id

{
  "action": "load_game_script",
  "place_id": "18441747385",
  "key_code": "MADARA-XXXX-XXXX-XXXX",
  "hwid": "user-hardware-id"
}
```

## 🛠️ Development Workflow

### Adding New Games
1. Create script file: `Roblox_Script/{PlaceID}.lua`
2. Add your game-specific code
3. Deploy: `npm run deploy-scripts`
4. Test: Load the enhanced loader in your game

### Script Template
```lua
-- Game Script for PlaceID: {PLACE_ID}
-- Game Name: {GAME_NAME}
-- Generated by Project Madara Deployment System

local Players = game:GetService("Players")
local LocalPlayer = Players.LocalPlayer

print("🎮 {GAME_NAME} Script Loaded!")
print("✅ Authenticated via Project Madara")
print("📍 PlaceID: {PLACE_ID}")

-- Your game-specific code here
-- ...

print("🎮 {GAME_NAME} Script Features Activated!")
```

### Testing
```bash
# Test entire deployment system
npm run test-deployment

# Test specific components
node scripts/test-deployment-system.js
```

## 🔧 Configuration

### Environment Variables
Required for deployment:
```env
SUPABASE_URL=your-supabase-url
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
SCRIPT_ENCRYPTION_KEY=your-encryption-key
```

### Netlify Configuration
Add to `netlify.toml`:
```toml
[build]
  command = "npm run build"
  publish = "dist"

[build.environment]
  NODE_VERSION = "18"
```

## 📈 Monitoring

### Access Logs
All script access is logged with:
- User identification (HWID/Key)
- IP address (hashed)
- Timestamp
- Success/failure status
- Error messages

### Database Tables
- `scripts`: Encrypted script storage
- `license_keys`: User authentication
- `script_access_logs`: Access monitoring
- `game_registry`: Supported games

## 🚨 Troubleshooting

### Common Issues

**Scripts not deploying:**
- Check environment variables
- Verify database connection
- Check script file naming (must be PlaceID.lua)

**Game not supported error:**
- Ensure script file exists for PlaceID
- Check database for script entry
- Verify script is marked as active

**Key validation failing:**
- Check key format (MADARA-XXXX-XXXX-XXXX)
- Verify key exists in database
- Check HWID binding

**Script loading errors:**
- Check encryption/decryption
- Verify script content integrity
- Check API endpoint availability

### Debug Commands
```bash
# Check deployment status
npm run test-deployment

# View database scripts
npm run manage-scripts -- --list

# Re-encrypt scripts
npm run encrypt-scripts

# Check logs
netlify functions:log secure-script
```

## 🎯 Best Practices

1. **Script Naming**: Always use PlaceID as filename
2. **Testing**: Test locally before production deployment
3. **Security**: Never commit encryption keys to version control
4. **Monitoring**: Regularly check access logs for anomalies
5. **Updates**: Use version control for script changes
6. **Backup**: Keep backups of important scripts

## 📞 Support

For issues or questions:
1. Check this documentation
2. Run diagnostic tests: `npm run test-deployment`
3. Check Netlify function logs
4. Review database entries
5. Contact development team

---

**Security Level**: Maximum  
**Last Updated**: 2025-01-30  
**Version**: 2.0.0
