--- EVOLVE 

local HttpService = game:GetService("HttpService")
local configFolder = "ProjectL"
local configFileName = "Evolve.config"
local configPath = string.format("%s/%s", configFolder, configFileName)
local isFirstTime = false

local configTemplate = {
    MainTab = {
        teleportFood = false,
        extraFood = false,
    },
    MiscTab = {
        btools = false,
        fly = false,
        clearBlurAndBall = false,
        serverHop = false,
    },
}

if not isfolder(configFolder) then
    makefolder(configFolder)
    isFirstTime = true
    print("[DEBUG] Config folder created")
end

if not isfile(configPath) then
    writefile(configPath, HttpService:JSONEncode(configTemplate))
    isFirstTime = true
    print("[DEBUG] Config file created")
end

getgenv().config = HttpService:JSONDecode(readfile(configPath)) or configTemplate

local function updateConfig()
    writefile(configPath, HttpService:JSONEncode(getgenv().config))
end

if isFirstTime then
    print("[INFO] First-time setup completed. Default configuration applied.")
else
    print("[INFO] Configuration loaded successfully.")
end

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local TeleportService = game:GetService("TeleportService")
local LocalPlayer = Players.LocalPlayer

local ProjectL = loadstring(game:HttpGet("https://raw.githubusercontent.com/idonthaveoneatm/Libraries/normal/quake/src"))()

local Main = ProjectL:Window({
    Title = "Project L V2 | Evolve",
})

local MainTab = Main:Tab({
    Name = "Farming",
    tabColor = Color3.fromHex("#71d1f5"),
    Image = "rbxassetid://10709769841"
})

local MiscTab = Main:Tab({
    Name = "Miscs",
    tabColor = Color3.fromHex("#34d793"),
    Image = "rbxassetid://10709819149"
})

local FoodSystem = {
    running = false,
    connections = {},
    originalPositions = {}
}

function FoodSystem:Initialize(folderName)
    local foodFolder = workspace:WaitForChild("MapObjects"):WaitForChild(folderName)
    
    -- Store original positions
    for _, food in ipairs(foodFolder:GetChildren()) do
        if food:IsA("BasePart") then
            self.originalPositions[food] = food.Position
        end
    end
    
    return foodFolder
end

function FoodSystem:TeleportFood(foodFolder)
    local character = LocalPlayer.Character
    if not character then return end
    
    local rootPart = character:FindFirstChild("HumanoidRootPart")
    if not rootPart then return end
    
    for _, food in ipairs(foodFolder:GetChildren()) do
        if food:IsA("BasePart") then
            food.Position = rootPart.Position
            food.CanCollide = false
        end
    end
end

function FoodSystem:Reset(foodFolder)
    for food, pos in pairs(self.originalPositions) do
        if food and food.Parent then
            food.Position = pos
            food.CanCollide = true
        end
    end
end

-- Regular Food Toggle
local regularFoodFolder = FoodSystem:Initialize("Food")
MainTab:Toggle({
    Name = "Teleport Foods to You",
    Default = getgenv().config.MainTab.teleportFood,
    Callback = function(value)
        FoodSystem.running = value
        getgenv().config.MainTab.teleportFood = value
        if value then
            local connection = RunService.RenderStepped:Connect(function()
                if FoodSystem.running then
                    FoodSystem:TeleportFood(regularFoodFolder)
                end
            end)
            table.insert(FoodSystem.connections, connection)
        else
            FoodSystem:Reset(regularFoodFolder)
            for _, conn in ipairs(FoodSystem.connections) do
                conn:Disconnect()
            end
            FoodSystem.connections = {}
        end
        updateConfig()
    end
})

-- Extra Food Toggle
local extraFoodFolder = FoodSystem:Initialize("ExtraFood")
MainTab:Toggle({
    Name = "Extra Foods",
    Default = getgenv().config.MainTab.extraFood,
    Callback = function(value)
        getgenv().config.MainTab.extraFood = value
        FoodSystem.running = value
        
        if value then
            local connection = RunService.RenderStepped:Connect(function()
                if FoodSystem.running then
                    FoodSystem:TeleportFood(extraFoodFolder)
                end
            end)
            table.insert(FoodSystem.connections, connection)
        else
            FoodSystem:Reset(extraFoodFolder)
            for _, conn in ipairs(FoodSystem.connections) do
                conn:Disconnect()
            end
            FoodSystem.connections = {}
        end
        updateConfig()
    end
})

-- Building Grid System
local BuildingSystem = {
    selectedPlayers = {}
}

function BuildingSystem:GetPlayers()
    local players = {}
    for _, child in ipairs(workspace.BuildingGrids:GetChildren()) do
        table.insert(players, child.Name)
    end
    return players
end

MiscTab:Label("Warning: This will take your DNA once dead.")
MiscTab:Dropdown({
    Name = "Players to Steal",
    Default = BuildingSystem:GetPlayers()[1] or "",
    Items = BuildingSystem:GetPlayers(),
    Callback = function(Value)
        BuildingSystem.selectedPlayers = {Value}
    end
})

MiscTab:Toggle({
    Name = "Steal Build",
    Default = false,
    Callback = function(Value)
        if Value then
            for _, playerName in pairs(BuildingSystem.selectedPlayers) do
                if workspace.BuildingGrids:FindFirstChild(playerName) then
                    workspace.BuildingGrids[playerName].Remotes.Confirm:InvokeServer()
                end
            end
        end
    end
})

-- Safe Zone Teleport
local originalPosition = nil

MainTab:Toggle({
    Name = "Teleport to Safe Zone",
    Default = false,
    Callback = function(Value)
        if Value and LocalPlayer.Character then
            originalPosition = LocalPlayer.Character.PrimaryPart.Position
            local highAltitudePosition = Vector3.new(1000000, 100000, 100000)
            LocalPlayer.Character:MoveTo(highAltitudePosition)
        elseif not Value and originalPosition and LocalPlayer.Character then
            LocalPlayer.Character:MoveTo(originalPosition)
        end
    end
})

-- Auto Teleport
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local LocalPlayer = Players.LocalPlayer

local looping = false
local selectedPlayer = nil
local teleportConnection = nil

local function GetPlayerNames()
    local playerNames = {}
    local creaturesFolder = workspace:FindFirstChild("Creatures")
    if creaturesFolder then
        for _, creature in ipairs(creaturesFolder:GetChildren()) do
            if creature:IsA("Model") and creature:FindFirstChild("Humanoid") then
                table.insert(playerNames, creature.Name)
            end
        end
    end
    return playerNames
end

local function TeleportPlayer(localPlayer, targetHRP)
    if targetHRP and localPlayer.Character then
        local localHRP = localPlayer.Character:FindFirstChild("HumanoidRootPart")
        if localHRP then
            localHRP.CFrame = targetHRP.CFrame
        end
    end
end

local function StartAutoTeleport(localPlayer)
    if teleportConnection then teleportConnection:Disconnect() end
    
    teleportConnection = RunService.Heartbeat:Connect(function()
        if selectedPlayer and selectedPlayer.Character then
            local targetHRP = selectedPlayer.Character:FindFirstChild("HumanoidRootPart")
            if targetHRP then
                TeleportPlayer(localPlayer, targetHRP)
            end
        end
    end)
end

local function StopAutoTeleport()
    if teleportConnection then
        teleportConnection:Disconnect()
        teleportConnection = nil
        looping = false
    end
end

MainTab:Dropdown({
    Name = "Select Player to Teleport",
    Default = "1",
    Items = GetPlayerNames(),
    Callback = function(Value)
        selectedPlayer = Players:FindFirstChild(Value)
    end
})

MainTab:Toggle({
    Name = "Auto Teleport",
    Default = false,
    Callback = function(Value)
        local localPlayer = Players.LocalPlayer
        
        if Value and localPlayer.Character then
            StartAutoTeleport(localPlayer)
        else
            StopAutoTeleport()
        end
    end
})

-- Player ESP
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local LocalPlayer = Players.LocalPlayer

local billboards = {}
local textSize = 16
local defaultColor = Color3.fromRGB(0, 255, 0)

local function createBillboard(entity)
    if not entity or not entity:IsA("Model") or not entity:FindFirstChild("Humanoid") then
        return
    end

    local billboard = Instance.new("BillboardGui")
    billboard.Name = "BillboardGui"
    billboard.Size = UDim2.new(2, 0, 1, 0)
    billboard.StudsOffset = Vector3.new(0, 3, 0)
    billboard.AlwaysOnTop = true
    billboard.Adornee = entity
    billboard.Parent = entity
    
    local textLabel = Instance.new("TextLabel")
    textLabel.Text = entity.Name
    textLabel.BackgroundTransparency = 1
    textLabel.TextColor3 = defaultColor
    textLabel.Size = UDim2.new(1, 0, 1, 0)
    textLabel.TextScaled = true
    textLabel.Font = Enum.Font.GothamBold
    textLabel.TextStrokeTransparency = 0.5
    textLabel.TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
    textLabel.Parent = billboard
    
    local function updateLabel()
        if entity and entity:IsA("Model") and entity:FindFirstChild("Humanoid") then
            local humanoid = entity.Humanoid
            if humanoid.Health <= 0 then
                textLabel.TextColor3 = Color3.fromRGB(255, 0, 0)
                textLabel.Text = string.format("%s\nHealth: %d (Dead)", entity.Name, humanoid.Health)
            else
                textLabel.TextColor3 = defaultColor
                textLabel.Text = string.format("%s\nHealth: %d", entity.Name, humanoid.Health)
            end
        end
    end
    
    if entity:FindFirstChild("Humanoid") then
        entity.Humanoid:GetPropertyChangedSignal("Health"):Connect(updateLabel)
    end
    
    updateLabel()
    table.insert(billboards, billboard)
end

local function handleToggle(value)
    local itemFolder = workspace.Creatures
    
    if value then
        for _, v in ipairs(itemFolder:GetChildren()) do
            if v:IsA("Model") and not v:FindFirstChild("BillboardGui") then
                createBillboard(v)
            end
        end
        
        itemFolder.ChildAdded:Connect(function(child)
            if child:IsA("Model") and not child:FindFirstChild("BillboardGui") then
                createBillboard(child)
            end
        end)
    else
        for _, billboard in ipairs(billboards) do
            billboard:Destroy()
        end
        billboards = {}
    end
end

MiscTab:Toggle({
    Name = "Player ESP",
    Default = false,
    Callback = function(Value)
        handleToggle(Value)
    end
})

-- Btools
local player = game.Players.LocalPlayer
local mouse = player:GetMouse()
local isBtoolsEnabled = false
local connection

local function teleportToClickedPosition()
    if isBtoolsEnabled then
        local targetPosition = mouse.Hit.p
        player.Character:SetPrimaryPartCFrame(CFrame.new(targetPosition))
    end
end

local function toggleBtools(value)
    isBtoolsEnabled = value
    if value then
        connection = mouse.Button1Down:Connect(teleportToClickedPosition)
    elseif connection then
        connection:Disconnect()
        connection = nil
    end
end

MiscTab:Toggle({
    Name = "Btools (Click To Teleport)",
    Default = false,
    Callback = toggleBtools
})

-- Fly
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local LocalPlayer = Players.LocalPlayer

local flyingSpeed = 50
local FLYING = false
local CONTROL = {F = 0, B = 0, L = 0, R = 0, Q = 0, E = 0}
local speedMultiplier = 1
local flyKeyDown, flyKeyUp

local function getRoot(character)
    return character:FindFirstChildOfClass("HumanoidRootPart") or character.PrimaryPart or character:FindFirstChild("Torso")
end

local function smoothCameraTransition(camera, character)
    camera.CameraType = Enum.CameraType.Scriptable
    local offset = CFrame.new(0, 5, -10)
    camera.CFrame = character.PrimaryPart.CFrame * offset
end

local function handleFlying()
    local character = LocalPlayer.Character
    local root = getRoot(character)
    
    local BG = Instance.new("BodyGyro")
    local BV = Instance.new("BodyVelocity")
    BG.P = 9e4
    BG.MaxTorque = Vector3.new(9e9, 9e9, 9e9)
    BG.Parent = root
    
    BV.MaxForce = Vector3.new(9e9, 9e9, 9e9)
    BV.Parent = root
    
    while FLYING do
        wait()
        BV.Velocity = (workspace.CurrentCamera.CoordinateFrame.lookVector * (CONTROL.F + CONTROL.B) * flyingSpeed * speedMultiplier) +
                      (workspace.CurrentCamera.CoordinateFrame * CFrame.new(CONTROL.L + CONTROL.R, (CONTROL.Q + CONTROL.E) * 2, 0).p - workspace.CurrentCamera.CoordinateFrame.p) *
                      flyingSpeed * speedMultiplier
        
        BG.CFrame = workspace.CurrentCamera.CoordinateFrame
        
        local ray = Ray.new(root.Position, BV.Velocity.Unit * 5)
        local hit, position = workspace:FindPartOnRay(ray, character)
        if hit and hit:IsA("Part") then
            BV.Velocity = Vector3.new(0, 0, 0)
        end
    end
    
    BG:Destroy()
    BV:Destroy()
end

local function startFlying()
    FLYING = true
    handleFlying()
end

local function stopFlying()
    FLYING = false
    if LocalPlayer.Character then
        local humanoid = LocalPlayer.Character:FindFirstChildOfClass("Humanoid")
        if humanoid then
            humanoid.PlatformStand = false
        end
    end
    workspace.CurrentCamera.CameraType = Enum.CameraType.Custom
end

flyKeyDown = UserInputService.InputBegan:Connect(function(KEY)
    if KEY.KeyCode == Enum.KeyCode.W then
        CONTROL.F = 1
    elseif KEY.KeyCode == Enum.KeyCode.S then
        CONTROL.B = -1
    elseif KEY.KeyCode == Enum.KeyCode.A then
        CONTROL.L = -1
    elseif KEY.KeyCode == Enum.KeyCode.D then
        CONTROL.R = 1
    elseif KEY.KeyCode == Enum.KeyCode.E then
        CONTROL.Q = 1
    elseif KEY.KeyCode == Enum.KeyCode.Q then
        CONTROL.E = -1
    end
end)

flyKeyUp = UserInputService.InputEnded:Connect(function(KEY)
    if KEY.KeyCode == Enum.KeyCode.W then
        CONTROL.F = 0
    elseif KEY.KeyCode == Enum.KeyCode.S then
        CONTROL.B = 0
    elseif KEY.KeyCode == Enum.KeyCode.A then
        CONTROL.L = 0
    elseif KEY.KeyCode == Enum.KeyCode.D then
        CONTROL.R = 0
    elseif KEY.KeyCode == Enum.KeyCode.E then
        CONTROL.Q = 0
    elseif KEY.KeyCode == Enum.KeyCode.Q then
        CONTROL.E = 0
    end
end)

MiscTab:Toggle({
    Name = "Fly",
    Default = false,
    Callback = function(Value)
        if Value then
            startFlying()
            print("Flying mode activated!")
        else
            stopFlying()
            print("Flying mode deactivated!")
        end
    end
})

MainTab:Toggle({
    Name = "Clear Blur and Ball",
    Default = false,
    Callback = function(Value)
        local function setTransparency(object, targetTransparency)
            if object and object.Transparency ~= targetTransparency then
                object.Transparency = targetTransparency
            end
        end

        local function setDensity(atmosphere, targetDensity)
            if atmosphere and atmosphere.Density ~= targetDensity then
                atmosphere.Density = targetDensity
            end
        end

        local playersFolder = workspace:WaitForChild("Creatures")
        local localPlayerModel = playersFolder:WaitForChild(game.Players.LocalPlayer.Name)

        task.wait(4)

        local Atmosphere = game:GetService("Lighting"):FindFirstChild("Atmosphere")
        local VisionRange = workspace:FindFirstChild("FX") and workspace.FX:FindFirstChild("VisionRange")
        local visionRangeOne = VisionRange and VisionRange:FindFirstChild("One")
        local visionRangeTwo = VisionRange and VisionRange:FindFirstChild("Two")

        if Value then
            -- Clear blur and ball
            setTransparency(VisionRange, 1)
            setTransparency(visionRangeOne, 1)
            setTransparency(visionRangeTwo, 1)
            setDensity(Atmosphere, 0)
        else
            -- Restore blur and ball
            setTransparency(VisionRange, 0)
            setTransparency(visionRangeOne, 0)
            setTransparency(visionRangeTwo, 0)
            setDensity(Atmosphere, 1)
        end
    end
})


-- Server Hop
MiscTab:Button({
    Name = "ServerHop",
    Callback = function()
        local function teleportToServer(placeId, serverId)
            if serverId then
                game:GetService("TeleportService"):TeleportToPlaceInstance(placeId, serverId)
            end
        end
        
        local servers = game:GetService("HttpService"):JSONDecode(game:HttpGet("https://games.roblox.com/v1/games/" .. game.PlaceId .. "/servers/Public?sortOrder=Asc&limit=100"))
        local serverFound = false
        
        for _, server in ipairs(servers.data) do
            if server.playing < server.maxPlayers and server.id ~= game.JobId then
                serverFound = true
                teleportToServer(game.PlaceId, server.id)
                break
            end
        end
        
        if not serverFound then
            print("No suitable server found")
        end
    end
})


local Credits =
    Main:Tab(
    {
        Name = "Credits",
        Image = "rbxassetid://10747373176"
    }
)


Credits:Label("UI: griffindoescooking")
Credits:Label("Script: Project L")

Credits:Label("Click the buttons below to copy the discord server invite.")
Credits:Button(
    {
        Name = "Project L",
        Callback = function()
            setclipboard("https://discord.gg/RQ7Dj4XdE5")
        end
    }
)

Credits:Button(
    {
        Name = "Griffin Discord",
        Callback = function()
            setclipboard("https://discord.gg/griffin")
        end
    }
)



local groupId = 16879177
local requiredRankNames = {"Moderator", "Owner", "Admin"}
local localPlayer = game.Players.LocalPlayer
local toggleEnabled = false
local actionOnModJoin = "Rejoin The Game"

local function checkAndRejoin()
    if toggleEnabled then
        for _, player in ipairs(game.Players:GetPlayers()) do
            local success, roleName = pcall(function()
                return player:GetRoleInGroup(groupId)
            end)
            
            if success then
                print(player.Name .. "'s role in group is: " .. roleName)
                
                for _, requiredRankName in ipairs(requiredRankNames) do
                    if roleName == requiredRankName then
                        if actionOnModJoin == "Rejoin The Game" then
                            game:GetService("TeleportService"):Teleport(game.PlaceId)
                        elseif actionOnModJoin == "Teleport to Void" then
                            localPlayer.Character:MoveTo(Vector3.new(1000000, 1000000, 1000000))
                        end
                        return 
                    end
                end
            else
                warn("Failed to get player's group role: " .. roleName)
            end
        end
    end
end

game.Players.PlayerAdded:Connect(function(player)
    print("Player added: " .. player.Name)
    checkAndRejoin()
end)

local function toggleCallback(value)
    toggleEnabled = value
    print("Toggle enabled: " .. tostring(toggleEnabled))
    
    if toggleEnabled then
        checkAndRejoin()
    end
end

MainTab:Dropdown({
    Name = "What to do when mod join?",
    Default = "Rejoin The Game",
    Items = {"Rejoin The Game", "Teleport to Void"},
    Callback = function(Value)
        actionOnModJoin = Value
        print("Action on mod join set to: " .. actionOnModJoin)
    end    
})

MainTab:Toggle({
    Name = "Activate Check for Mod",
    Default = false,
    Callback = toggleCallback
})


-- Update billboards if new creatures are added
workspace.Creatures.ChildAdded:Connect(function(child)
    createBillboard(child)
end)

-- Clean up billboards when creatures are removed
workspace.Creatures.ChildRemoved:Connect(function(child)
    local billboard = child:FindFirstChild("BillboardGui")
    if billboard then
        billboard:Destroy()
    end
end)

