-- Game Script for PlaceID: 18441747385
-- Game Name: Custom Game Example
-- Generated by Project Madara Deployment System

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")
local Workspace = game:GetService("Workspace")

local LocalPlayer = Players.LocalPlayer

print("🎮 Custom Game Script Loaded!")
print("✅ Authenticated via Project Madara")
print("📍 PlaceID: 18441747385")

-- ========================================
-- CUSTOM SCRIPT FEATURES
-- ========================================

-- Speed boost feature
local function applySpeedBoost()
    if LocalPlayer.Character and LocalPlayer.Character:FindFirstChild("Humanoid") then
        LocalPlayer.Character.Humanoid.WalkSpeed = 50
        print("🏃 Speed boost activated! (WalkSpeed: 50)")
    end
end

-- Jump power boost
local function applyJumpBoost()
    if LocalPlayer.Character and LocalPlayer.Character:FindFirstChild("Humanoid") then
        LocalPlayer.Character.Humanoid.JumpPower = 100
        print("🦘 Jump boost activated! (JumpPower: 100)")
    end
end

-- Player ESP system
local function createPlayerESP()
    for _, player in pairs(Players:GetPlayers()) do
        if player ~= LocalPlayer and player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
            -- Remove existing ESP
            local existingESP = player.Character.HumanoidRootPart:FindFirstChild("PlayerESP")
            if existingESP then
                existingESP:Destroy()
            end
            
            -- Create new ESP
            local billboard = Instance.new("BillboardGui")
            billboard.Name = "PlayerESP"
            billboard.Size = UDim2.new(0, 100, 0, 50)
            billboard.StudsOffset = Vector3.new(0, 3, 0)
            billboard.Parent = player.Character.HumanoidRootPart

            local nameLabel = Instance.new("TextLabel")
            nameLabel.Size = UDim2.new(1, 0, 1, 0)
            nameLabel.BackgroundTransparency = 1
            nameLabel.Text = player.Name
            nameLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
            nameLabel.TextScaled = true
            nameLabel.Font = Enum.Font.GothamBold
            nameLabel.TextStrokeTransparency = 0
            nameLabel.TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
            nameLabel.Parent = billboard
            
            -- Distance indicator
            local distance = (player.Character.HumanoidRootPart.Position - LocalPlayer.Character.HumanoidRootPart.Position).Magnitude
            nameLabel.Text = player.Name .. "\n[" .. math.floor(distance) .. " studs]"
        end
    end
end

-- Teleportation system
local teleportLocations = {
    spawn = Vector3.new(0, 10, 0),
    sky = Vector3.new(0, 100, 0),
    underground = Vector3.new(0, -50, 0)
}

local function teleportTo(locationName)
    if LocalPlayer.Character and LocalPlayer.Character:FindFirstChild("HumanoidRootPart") then
        local location = teleportLocations[locationName]
        if location then
            LocalPlayer.Character.HumanoidRootPart.CFrame = CFrame.new(location)
            print("📍 Teleported to: " .. locationName)
        end
    end
end

-- Fly mode
local flying = false
local flySpeed = 50
local bodyVelocity = nil
local bodyAngularVelocity = nil

local function toggleFly()
    if not LocalPlayer.Character or not LocalPlayer.Character:FindFirstChild("HumanoidRootPart") then
        return
    end
    
    flying = not flying
    
    if flying then
        -- Enable fly mode
        local humanoidRootPart = LocalPlayer.Character.HumanoidRootPart
        
        bodyVelocity = Instance.new("BodyVelocity")
        bodyVelocity.MaxForce = Vector3.new(4000, 4000, 4000)
        bodyVelocity.Velocity = Vector3.new(0, 0, 0)
        bodyVelocity.Parent = humanoidRootPart
        
        bodyAngularVelocity = Instance.new("BodyAngularVelocity")
        bodyAngularVelocity.MaxTorque = Vector3.new(4000, 4000, 4000)
        bodyAngularVelocity.AngularVelocity = Vector3.new(0, 0, 0)
        bodyAngularVelocity.Parent = humanoidRootPart
        
        print("✈️ Fly mode enabled! Use WASD to fly")
    else
        -- Disable fly mode
        if bodyVelocity then
            bodyVelocity:Destroy()
            bodyVelocity = nil
        end
        if bodyAngularVelocity then
            bodyAngularVelocity:Destroy()
            bodyAngularVelocity = nil
        end
        print("🚶 Fly mode disabled")
    end
end

-- Key bindings
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    
    if input.KeyCode == Enum.KeyCode.E then
        createPlayerESP()
        print("👁️ Player ESP refreshed")
    elseif input.KeyCode == Enum.KeyCode.R then
        applySpeedBoost()
        applyJumpBoost()
    elseif input.KeyCode == Enum.KeyCode.T then
        teleportTo("spawn")
    elseif input.KeyCode == Enum.KeyCode.Y then
        teleportTo("sky")
    elseif input.KeyCode == Enum.KeyCode.U then
        teleportTo("underground")
    elseif input.KeyCode == Enum.KeyCode.F then
        toggleFly()
    end
end)

-- Fly movement controls
UserInputService.InputChanged:Connect(function(input, gameProcessed)
    if gameProcessed or not flying or not bodyVelocity then return end
    
    if input.UserInputType == Enum.UserInputType.Keyboard then
        local camera = Workspace.CurrentCamera
        local moveVector = Vector3.new(0, 0, 0)
        
        if UserInputService:IsKeyDown(Enum.KeyCode.W) then
            moveVector = moveVector + camera.CFrame.LookVector
        end
        if UserInputService:IsKeyDown(Enum.KeyCode.S) then
            moveVector = moveVector - camera.CFrame.LookVector
        end
        if UserInputService:IsKeyDown(Enum.KeyCode.A) then
            moveVector = moveVector - camera.CFrame.RightVector
        end
        if UserInputService:IsKeyDown(Enum.KeyCode.D) then
            moveVector = moveVector + camera.CFrame.RightVector
        end
        if UserInputService:IsKeyDown(Enum.KeyCode.Space) then
            moveVector = moveVector + Vector3.new(0, 1, 0)
        end
        if UserInputService:IsKeyDown(Enum.KeyCode.LeftShift) then
            moveVector = moveVector - Vector3.new(0, 1, 0)
        end
        
        bodyVelocity.Velocity = moveVector * flySpeed
    end
end)

-- Initialize features
spawn(function()
    wait(1) -- Wait for character to load
    applySpeedBoost()
    applyJumpBoost()
    createPlayerESP()
    
    -- Periodic ESP refresh
    while wait(5) do
        if LocalPlayer.Character then
            createPlayerESP()
        end
    end
end)

print("🎮 Custom Game Script Features Activated!")
print("📋 Controls:")
print("   - E: Refresh Player ESP")
print("   - R: Reapply Speed/Jump Boost")
print("   - T: Teleport to Spawn")
print("   - Y: Teleport to Sky")
print("   - U: Teleport Underground")
print("   - F: Toggle Fly Mode")
print("   - WASD + Space/Shift: Fly Controls (when flying)")
print("✅ Script loaded via Project Madara Secure System")
