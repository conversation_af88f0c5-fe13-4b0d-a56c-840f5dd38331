# Roblox Scripts Directory

This directory contains game-specific Roblox scripts that are automatically deployed to the secure backend system.

## File Naming Convention

Scripts must be named using their PlaceID with the `.lua` extension:
- `18441747385.lua` - Script for PlaceID 18441747385
- `142823291.lua` - <PERSON><PERSON><PERSON> for Murder Mystery 2 (PlaceID 142823291)
- `537413528.lua` - Script for Build A Boat For Treasure (PlaceID 537413528)

## Automatic Deployment

When you run `netlify deploy --prod`, the deployment system will:

1. **Scan this directory** for all `.lua` files
2. **Extract PlaceID** from filename
3. **Encrypt script content** using AES-256-GCM
4. **Upload to secure database** with proper metadata
5. **Update game registry** with support information
6. **Generate deployment report** with success/failure status

## Script Structure

Each script should follow this basic structure:

```lua
-- Game Script for PlaceID: [PLACE_ID]
-- Game Name: [GAME_NAME]
-- Generated by Project Madara Deployment System

local Players = game:GetService("Players")
local LocalPlayer = Players.LocalPlayer

print("🎮 [GAME_NAME] Script Loaded!")
print("✅ Authenticated via Project Madara")
print("📍 PlaceID: [PLACE_ID]")

-- Your game-specific code here
-- ...

print("🎮 [GAME_NAME] Script Features Activated!")
```

## Security Features

All scripts in this directory are automatically:
- ✅ **Encrypted** with AES-256-GCM before storage
- ✅ **Obfuscated** during delivery to prevent reverse engineering
- ✅ **Protected** with anti-tampering measures
- ✅ **Monitored** with comprehensive access logging
- ✅ **Validated** with integrity checks

## Supported Games

The deployment system will automatically detect and support any game with a script file in this directory. Current games:

- Add your `.lua` files here and they will be automatically detected!

## Usage

1. **Add your script**: Create a new `.lua` file named with the PlaceID
2. **Deploy**: Run `netlify deploy --prod` to deploy all scripts
3. **Test**: Use the main loader to test your deployed script
4. **Monitor**: Check the deployment logs for any issues

## Example Files

You can add example files like:
- `18441747385.lua` - Your custom game script
- `142823291.lua` - Murder Mystery 2 script
- `537413528.lua` - Build A Boat script

The system will automatically handle encryption, deployment, and secure delivery!
