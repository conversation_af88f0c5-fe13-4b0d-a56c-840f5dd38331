-- PROJECT MADARA - ENHANCED MAIN LOADER
-- Automated Script Deployment & Loading System
-- Security Level: Maximum

-- ========================================
-- SECURITY INITIALIZATION
-- ========================================

local function initializeSecurity()
    local success, result = pcall(function()
        return game:GetService("HttpService"):JSONEncode({security_check = true})
    end)
    if not success then
        error("Security initialization failed")
    end
    
    if not game or not game.GetService then
        error("Invalid execution environment detected")
    end
    
    local requiredServices = {"HttpService", "Players", "RunService", "UserInputService"}
    for _, serviceName in pairs(requiredServices) do
        local service = game:GetService(serviceName)
        if not service then
            error("Required service not available: " .. serviceName)
        end
    end
    
    return true
end

-- Initialize security
local securityInitialized = initializeSecurity()
if not securityInitialized then
    error("Security initialization failed")
end

-- ========================================
-- GAME SUPPORT CHECKING
-- ========================================

local function checkGameSupport()
    local placeId = tostring(game.PlaceId)
    print("🔍 Checking game support for PlaceID: " .. placeId)
    
    -- Check if game is supported via API
    local url = "https://projectmadara.com/.netlify/functions/secure-script?action=check_game_support&place_id=" .. placeId
    
    local success, response = pcall(function()
        return game:GetService("HttpService"):RequestAsync({
            Url = url,
            Method = "GET",
            Headers = {
                ["User-Agent"] = "ProjectMadara/EnhancedLoader",
                ["X-Security-Level"] = "enhanced"
            }
        })
    end)
    
    if not success then
        print("❌ Failed to check game support")
        return false, "Unable to connect to support check service"
    end
    
    if response.StatusCode == 200 then
        local result = game:GetService("HttpService"):JSONDecode(response.Body)
        if result.supported then
            print("✅ Game is supported: " .. (result.game_name or "Unknown Game"))
            return true, result
        else
            print("❌ Game not supported")
            return false, "This game is not currently supported by Project Madara"
        end
    else
        print("❌ Game support check failed")
        return false, "Game support check service unavailable"
    end
end

-- ========================================
-- LOCAL KEY CACHING SYSTEM
-- ========================================

local function getStoredKey()
    -- Try to get stored key from various methods
    local success, key = pcall(function()
        -- Method 1: Check if key is stored in workspace
        if workspace:FindFirstChild("MadaraKeyStorage") then
            local storage = workspace.MadaraKeyStorage
            if storage:FindFirstChild("StoredKey") then
                return storage.StoredKey.Value
            end
        end
        return nil
    end)
    
    if success and key and key ~= "" then
        print("🔑 Found stored license key")
        return key
    end
    
    return nil
end

local function storeKey(key)
    local success = pcall(function()
        -- Create storage in workspace
        local storage = workspace:FindFirstChild("MadaraKeyStorage")
        if not storage then
            storage = Instance.new("Folder")
            storage.Name = "MadaraKeyStorage"
            storage.Parent = workspace
        end
        
        local keyValue = storage:FindFirstChild("StoredKey")
        if not keyValue then
            keyValue = Instance.new("StringValue")
            keyValue.Name = "StoredKey"
            keyValue.Parent = storage
        end
        
        keyValue.Value = key
        print("💾 License key stored locally")
    end)
    
    return success
end

local function clearStoredKey()
    local success = pcall(function()
        local storage = workspace:FindFirstChild("MadaraKeyStorage")
        if storage then
            storage:Destroy()
        end
        print("🗑️ Stored license key cleared")
    end)
    
    return success
end

-- ========================================
-- ENHANCED HWID GENERATION
-- ========================================

local function getSecureHWID()
    local hwid = nil
    
    local success1, result1 = pcall(function()
        return game:GetService("RbxAnalyticsService"):GetClientId()
    end)
    
    if success1 and result1 and result1 ~= "" then
        hwid = result1
    else
        local success2, result2 = pcall(function()
            local HttpService = game:GetService("HttpService")
            local Players = game:GetService("Players")
            local LocalPlayer = Players.LocalPlayer
            
            if LocalPlayer then
                return HttpService:GenerateGUID(false)
            end
        end)
        
        if success2 and result2 then
            hwid = result2
        else
            error("Unable to generate secure HWID")
        end
    end
    
    return hwid
end

-- ========================================
-- KEY VALIDATION SYSTEM
-- ========================================

local function validateKey(key)
    if not key or #key == 0 then
        return false, "Valid key required"
    end
    
    -- Key format validation
    if not string.match(key, "^MADARA%-[A-Z0-9][A-Z0-9][A-Z0-9][A-Z0-9]%-[A-Z0-9][A-Z0-9][A-Z0-9][A-Z0-9]%-[A-Z0-9][A-Z0-9][A-Z0-9][A-Z0-9]$") then
        return false, "Invalid key format"
    end
    
    local hwid = getSecureHWID()
    print("🔑 Validating key: " .. key:sub(1, 15) .. "...")
    
    local url = "https://projectmadara.com/.netlify/functions/validate-key"
    local headers = {
        ["Content-Type"] = "application/json",
        ["X-Key-Code"] = key,
        ["X-HWID"] = hwid,
        ["X-Security-Level"] = "enhanced",
        ["User-Agent"] = "ProjectMadara/EnhancedLoader"
    }
    
    local success, response = pcall(function()
        return game:GetService("HttpService"):RequestAsync({
            Url = url,
            Method = "POST",
            Headers = headers,
            Body = game:GetService("HttpService"):JSONEncode({
                key_code = key,
                hwid = hwid,
                place_id = tostring(game.PlaceId),
                security_check = true,
                loader_version = "enhanced"
            })
        })
    end)
    
    if not success then
        return false, "Key validation request failed"
    end
    
    if response.StatusCode ~= 200 then
        return false, "Key validation failed: " .. (response.StatusMessage or "Unknown error")
    end
    
    local validationResult = game:GetService("HttpService"):JSONDecode(response.Body)
    
    if not validationResult.valid then
        return false, validationResult.error or "Invalid key"
    end
    
    print("✅ Key validation successful")
    return true, validationResult
end

-- ========================================
-- SECURE SCRIPT LOADING
-- ========================================

local function loadGameScript(key)
    local placeId = tostring(game.PlaceId)
    local hwid = getSecureHWID()
    
    print("🎮 Loading secure script for PlaceID: " .. placeId)
    
    local url = "https://projectmadara.com/.netlify/functions/secure-script"
    local headers = {
        ["Content-Type"] = "application/json",
        ["X-Key-Code"] = key,
        ["X-HWID"] = hwid,
        ["X-Security-Level"] = "maximum",
        ["User-Agent"] = "ProjectMadara/EnhancedLoader"
    }
    
    local requestBody = {
        action = "load_game_script",
        place_id = placeId,
        key_code = key,
        hwid = hwid,
        security_level = "enhanced",
        loader_version = "enhanced"
    }
    
    local success, response = pcall(function()
        return game:GetService("HttpService"):RequestAsync({
            Url = url,
            Method = "POST",
            Headers = headers,
            Body = game:GetService("HttpService"):JSONEncode(requestBody)
        })
    end)
    
    if not success then
        return false, "Script loading request failed"
    end
    
    if response.StatusCode ~= 200 then
        return false, "Script loading failed: " .. (response.StatusMessage or "Unknown error")
    end
    
    local contentType = response.Headers["content-type"] or ""
    
    if string.find(contentType, "application/json") then
        local result = game:GetService("HttpService"):JSONDecode(response.Body)
        
        if result.error then
            return false, "Script loading failed: " .. result.error
        end
        
        if result.script_url then
            local scriptContent = game:HttpGet(result.script_url)
            local loadSuccess, loadError = pcall(loadstring(scriptContent))
            
            if loadSuccess then
                print("✅ Secure script loaded successfully!")
                return true, "Script loaded successfully"
            else
                return false, "Script execution failed: " .. tostring(loadError)
            end
        end
    else
        local scriptContent = response.Body
        
        if scriptContent and scriptContent ~= "" and not string.find(scriptContent, "error") then
            local loadSuccess, loadError = pcall(loadstring(scriptContent))
            
            if loadSuccess then
                print("✅ Secure script loaded successfully!")
                return true, "Script loaded successfully"
            else
                return false, "Script execution failed: " .. tostring(loadError)
            end
        else
            return false, "No valid script content received"
        end
    end
    
    return false, "Unknown error occurred"
end

-- ========================================
-- MAIN LOADER LOGIC FLOW
-- ========================================

local function executeMainLoader()
    print("🚀 Project Madara Enhanced Main Loader")
    print("🔄 Automated Script Deployment System")
    print("==========================================")
    
    -- Step 1: Check game support
    print("\n📋 Step 1: Checking game support...")
    local gameSupported, gameInfo = checkGameSupport()
    
    if not gameSupported then
        print("❌ " .. (gameInfo or "Game not supported"))
        print("💡 This game is not currently supported by Project Madara")
        print("📧 Contact support to request game support")
        return
    end
    
    print("✅ Game supported: " .. (gameInfo.game_name or "Unknown Game"))
    
    -- Step 2: Check for stored key
    print("\n📋 Step 2: Checking for stored license key...")
    local storedKey = getStoredKey()
    
    if storedKey then
        print("🔑 Found stored license key, validating...")
        local keyValid, validationResult = validateKey(storedKey)
        
        if keyValid then
            print("✅ Stored key is valid, proceeding to script loading...")
            
            -- Step 3: Load game script
            print("\n📋 Step 3: Loading game script...")
            local scriptLoaded, loadResult = loadGameScript(storedKey)
            
            if scriptLoaded then
                print("🎉 Script loaded successfully!")
                return
            else
                print("❌ Script loading failed: " .. loadResult)
                print("🔄 Clearing stored key and requesting new authentication...")
                clearStoredKey()
            end
        else
            print("❌ Stored key is invalid: " .. validationResult)
            print("🔄 Clearing stored key and requesting new authentication...")
            clearStoredKey()
        end
    else
        print("🔑 No stored license key found")
    end
    
    -- Step 4: Request key input (this would trigger the UI)
    print("\n📋 Step 4: License key required...")
    print("🔑 Please enter your Project Madara license key")
    print("💡 The key system UI will appear shortly...")
    
    -- Set global flag for UI to know we need key input
    _G.MADARA_NEEDS_KEY_INPUT = true
    _G.MADARA_GAME_INFO = gameInfo
    
    -- Load the key system UI
    local keySystemUrl = "https://projectmadara.com/.netlify/functions/secure-script?action=get_key_system_ui"
    local success, keySystemScript = pcall(function()
        return game:HttpGet(keySystemUrl)
    end)
    
    if success and keySystemScript and keySystemScript ~= "" then
        local uiSuccess, uiError = pcall(loadstring(keySystemScript))
        if not uiSuccess then
            print("❌ Failed to load key system UI: " .. tostring(uiError))
        end
    else
        print("❌ Failed to load key system UI")
    end
end

-- ========================================
-- GLOBAL FUNCTIONS FOR UI INTEGRATION
-- ========================================

_G.MADARA_ENHANCED_LOADER = {
    validateAndStoreKey = function(key)
        local valid, result = validateKey(key)
        if valid then
            storeKey(key)
            print("✅ Key validated and stored successfully!")
            
            -- Load the game script
            local scriptLoaded, loadResult = loadGameScript(key)
            if scriptLoaded then
                print("🎉 Game script loaded successfully!")
                return true, "Success"
            else
                return false, loadResult
            end
        else
            return false, result
        end
    end,
    
    clearStoredKey = clearStoredKey,
    getGameInfo = function()
        return _G.MADARA_GAME_INFO
    end,
    
    loadGameScript = loadGameScript
}

-- ========================================
-- EXECUTE MAIN LOADER
-- ========================================

executeMainLoader()
