import { createClient } from '@supabase/supabase-js';
import crypto from 'crypto';
import zlib from 'zlib';
import { validateSessionToken } from './utils/auth.js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Enhanced encryption configuration
const ENCRYPTION_KEY = process.env.SCRIPT_ENCRYPTION_KEY || crypto.randomBytes(32);
const ENCRYPTION_ALGORITHM = 'aes-256-gcm';
const IV_LENGTH = 16;
const TAG_LENGTH = 16;
const SALT_LENGTH = 32;

// In-memory cache for frequently accessed scripts (production should use Redis)
const scriptCache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

// Enhanced CORS headers for Roblox compatibility
const allowCors = (fn) => async (request, context) => {
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Key-Code, X-Session-Token, X-Requested-With, Accept, Origin, User-Agent',
        'Access-Control-Allow-Credentials': 'false',
        'Access-Control-Max-Age': '86400',
        'Content-Type': 'text/plain', // Default to text/plain for script content
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
    };

    if (request.method === 'OPTIONS') {
        return new Response('', { status: 200, headers });
    }

    const result = await fn(request, context, headers);
    return result;
};

// Utility functions
const getClientIP = (request) => {
    return request.headers.get('x-forwarded-for')?.split(',')[0] || 
           request.headers.get('x-real-ip') || 
           'unknown';
};

const hashIP = (ip) => {
    return crypto.createHash('sha256').update(ip + process.env.IP_SALT || 'default_salt').digest('hex');
};

const generateAccessToken = () => {
    return crypto.randomBytes(32).toString('hex');
};

// Cache management functions
const getCachedScript = (scriptId) => {
    const cached = scriptCache.get(scriptId);
    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
        return cached.data;
    }
    return null;
};

const setCachedScript = (scriptId, data) => {
    scriptCache.set(scriptId, {
        data,
        timestamp: Date.now()
    });
};

// Enhanced encryption utilities
const encryptContent = (content) => {
    try {
        const iv = crypto.randomBytes(IV_LENGTH);
        const salt = crypto.randomBytes(SALT_LENGTH);
        const key = crypto.pbkdf2Sync(ENCRYPTION_KEY, salt, 100000, 32, 'sha512');

        const cipher = crypto.createCipher(ENCRYPTION_ALGORITHM, key);
        cipher.setAAD(Buffer.from('script-content'));

        let encrypted = cipher.update(content, 'utf8');
        encrypted = Buffer.concat([encrypted, cipher.final()]);

        const tag = cipher.getAuthTag();

        return {
            encrypted: Buffer.concat([salt, iv, tag, encrypted]).toString('base64'),
            hash: crypto.createHash('sha256').update(content).digest('hex')
        };
    } catch (error) {
        throw new Error('Encryption failed: ' + error.message);
    }
};

const decryptContent = (encryptedData) => {
    try {
        const buffer = Buffer.from(encryptedData, 'base64');
        const salt = buffer.slice(0, SALT_LENGTH);
        const iv = buffer.slice(SALT_LENGTH, SALT_LENGTH + IV_LENGTH);
        const tag = buffer.slice(SALT_LENGTH + IV_LENGTH, SALT_LENGTH + IV_LENGTH + TAG_LENGTH);
        const encrypted = buffer.slice(SALT_LENGTH + IV_LENGTH + TAG_LENGTH);

        const key = crypto.pbkdf2Sync(ENCRYPTION_KEY, salt, 100000, 32, 'sha512');

        const decipher = crypto.createDecipher(ENCRYPTION_ALGORITHM, key);
        decipher.setAAD(Buffer.from('script-content'));
        decipher.setAuthTag(tag);

        let decrypted = decipher.update(encrypted);
        decrypted = Buffer.concat([decrypted, decipher.final()]);

        return decrypted.toString('utf8');
    } catch (error) {
        throw new Error('Decryption failed: ' + error.message);
    }
};

// Compression utilities
const compressContent = (content) => {
    return new Promise((resolve, reject) => {
        zlib.gzip(content, (err, compressed) => {
            if (err) reject(err);
            else resolve(compressed);
        });
    });
};

const decompressContent = (compressed) => {
    return new Promise((resolve, reject) => {
        zlib.gunzip(compressed, (err, decompressed) => {
            if (err) reject(err);
            else resolve(decompressed.toString());
        });
    });
};

// Script obfuscation utilities
const obfuscateScript = (content) => {
    try {
        // Basic obfuscation techniques
        let obfuscated = content;

        // Add anti-debugging measures
        const antiDebug = `
-- Anti-debugging and tamper protection
local function checkIntegrity()
    local success, result = pcall(function()
        return game:GetService("HttpService"):JSONEncode({check = true})
    end)
    if not success then
        error("Security check failed")
    end
end
checkIntegrity()

-- Original script content:
`;

        // String obfuscation - encode strings in base64
        obfuscated = obfuscated.replace(/"([^"]+)"/g, (match, str) => {
            if (str.length > 3) {
                const encoded = Buffer.from(str).toString('base64');
                return `game:GetService("HttpService"):JSONDecode(game:GetService("HttpService"):GetAsync("data:text/plain;base64,${encoded}"))`;
            }
            return match;
        });

        // Variable name randomization (basic)
        const varMap = new Map();
        let varCounter = 0;

        obfuscated = obfuscated.replace(/local\s+([a-zA-Z_][a-zA-Z0-9_]*)/g, (match, varName) => {
            if (!varMap.has(varName)) {
                varMap.set(varName, `_${crypto.randomBytes(4).toString('hex')}`);
            }
            return `local ${varMap.get(varName)}`;
        });

        // Apply variable replacements
        for (const [original, obfuscatedName] of varMap) {
            const regex = new RegExp(`\\b${original}\\b`, 'g');
            obfuscated = obfuscated.replace(regex, obfuscatedName);
        }

        // Add integrity check at the end
        const integrityCheck = `
-- Integrity verification
local function verifyExecution()
    spawn(function()
        while wait(math.random(30, 60)) do
            checkIntegrity()
        end
    end)
end
verifyExecution()
`;

        return antiDebug + obfuscated + integrityCheck;

    } catch (error) {
        console.error('Obfuscation failed:', error);
        return content; // Return original if obfuscation fails
    }
};

// Validate license key
const validateLicenseKey = async (keyCode, hwid, ipHash) => {
    if (!keyCode) return { valid: false, error: 'No key provided' };
    
    try {
        const { data: key, error } = await supabase
            .from('license_keys')
            .select('*, hwid_bindings(*)')
            .eq('key_code', keyCode)
            .eq('is_active', true)
            .eq('is_revoked', false)
            .single();
            
        if (error || !key) {
            return { valid: false, error: 'Invalid or inactive key' };
        }
        
        if (new Date(key.expires_at) < new Date()) {
            return { valid: false, error: 'Key has expired' };
        }
        
        // Check HWID binding if provided
        if (hwid && key.hwid_bindings?.length > 0) {
            const validBinding = key.hwid_bindings.find(binding => 
                binding.hwid_hash === hwid && binding.is_active
            );
            if (!validBinding) {
                return { valid: false, error: 'HWID mismatch' };
            }
        }
        
        return { valid: true, key };
    } catch (error) {
        return { valid: false, error: 'Key validation failed' };
    }
};

// Log script access
const logScriptAccess = async (scriptId, userIdentifier, accessType, ipHash, userAgent, keyCode, success, errorMessage = null, fileSize = null) => {
    try {
        await supabase
            .from('script_access_logs')
            .insert({
                script_id: scriptId,
                user_identifier: userIdentifier,
                access_type: accessType,
                ip_hash: ipHash,
                user_agent: userAgent,
                key_code: keyCode,
                success,
                error_message: errorMessage,
                file_size_served: fileSize
            });
    } catch (error) {
        console.error('Failed to log script access:', error);
    }
};

// Generate secure loadstring URL
const generateSecureLoadstring = async (scriptId, keyCode, ipHash, userAgent) => {
    try {
        // Generate access token
        const token = generateAccessToken();
        const expiresAt = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
        
        // Store token in database
        await supabase
            .from('script_access_tokens')
            .insert({
                token,
                script_id: scriptId,
                user_identifier: keyCode,
                key_code: keyCode,
                expires_at: expiresAt.toISOString(),
                max_uses: 3, // Allow 3 uses for reliability
                ip_hash: ipHash,
                user_agent_hash: crypto.createHash('sha256').update(userAgent || '').digest('hex')
            });
            
        // Return secure loadstring
        const baseUrl = process.env.URL || 'https://projectmadara.com';
        return `loadstring(game:HttpGet("${baseUrl}/.netlify/functions/secure-script?token=${token}"))()`;

    } catch (error) {
        throw new Error('Failed to generate secure loadstring');
    }
};

// Get game script by PlaceId
const getGameScriptByPlaceId = async (placeId) => {
    try {
        // First check cache
        const cacheKey = `place_${placeId}`;
        const cached = getCachedScript(cacheKey);
        if (cached) {
            return { success: true, script: cached };
        }

        // Query database for game script
        const { data: script, error } = await supabase
            .from('scripts')
            .select('*')
            .eq('place_id', placeId)
            .eq('script_type', 'game_specific')
            .eq('is_active', true)
            .single();

        if (error || !script) {
            return { success: false, error: 'No script found for this game' };
        }

        // Cache the result
        setCachedScript(cacheKey, script);

        return { success: true, script };
    } catch (error) {
        return { success: false, error: error.message };
    }
};

// Get main loader script
const getMainLoaderScript = async (clearCache = false) => {
    try {
        if (!clearCache) {
            const cached = getCachedScript('main_loader');
            if (cached) {
                return { success: true, script: cached };
            }
        }

        const { data: script, error } = await supabase
            .from('scripts')
            .select('*')
            .eq('script_type', 'main_loader')
            .eq('is_main_script', true)
            .eq('is_active', true)
            .single();

        if (error || !script) {
            return { success: false, error: 'Main loader script not found' };
        }

        setCachedScript('main_loader', script);
        return { success: true, script };
    } catch (error) {
        return { success: false, error: error.message };
    }
};

// Main handler
const handler = async (request, context, corsHeaders) => {
    const url = new URL(request.url);
    const token = url.searchParams.get('token');
    const keyCode = request.headers.get('x-key-code') || url.searchParams.get('key_code');
    const hwid = request.headers.get('x-hwid') || url.searchParams.get('hwid');
    const scriptId = url.searchParams.get('script_id');
    const placeId = url.searchParams.get('place_id');
    const action = url.searchParams.get('action') || 'get_content';
    
    const clientIP = getClientIP(request);
    const ipHash = hashIP(clientIP);
    const userAgent = request.headers.get('user-agent') || '';
    
    try {
        if (action === 'get_game_script') {
            // Load game script by PlaceId
            const url = new URL(request.url);
            const placeId = url.searchParams.get('placeId');

            if (!placeId) {
                return new Response('warn("No PlaceId provided")', {
                    status: 400,
                    headers: { ...corsHeaders, 'Content-Type': 'text/plain' }
                });
            }

            // Game scripts mapped by PlaceId
            const gameScripts = {
                "17574618959": `-- Just a baseplate
-- PlaceId: 17574618959
-- URL: https://www.roblox.com/games/17574618959/Just-a-baseplate

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")
local Workspace = game:GetService("Workspace")

local LocalPlayer = Players.LocalPlayer

print("🎮 Just a baseplate Script Loaded!")
print("✅ Authenticated via Project Madara Centralized System")
print("📍 PlaceId: 17574618959")

-- ========================================
-- YOUR CUSTOM SCRIPT CODE GOES HERE
-- ========================================

-- Example: Speed boost
if LocalPlayer.Character and LocalPlayer.Character:FindFirstChild("Humanoid") then
    LocalPlayer.Character.Humanoid.WalkSpeed = 50
    print("🏃 Speed boost activated!")
end

-- Example: Jump power boost
if LocalPlayer.Character and LocalPlayer.Character:FindFirstChild("Humanoid") then
    LocalPlayer.Character.Humanoid.JumpPower = 100
    print("🦘 Jump boost activated!")
end

-- Example: Simple ESP for other players
local function createESP()
    for _, player in pairs(Players:GetPlayers()) do
        if player ~= LocalPlayer and player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
            local billboard = Instance.new("BillboardGui")
            billboard.Size = UDim2.new(0, 100, 0, 50)
            billboard.StudsOffset = Vector3.new(0, 3, 0)
            billboard.Parent = player.Character.HumanoidRootPart

            local nameLabel = Instance.new("TextLabel")
            nameLabel.Size = UDim2.new(1, 0, 1, 0)
            nameLabel.BackgroundTransparency = 1
            nameLabel.Text = player.Name
            nameLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
            nameLabel.TextScaled = true
            nameLabel.Font = Enum.Font.GothamBold
            nameLabel.Parent = billboard
        end
    end
end

-- Example: Key bindings
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end

    if input.KeyCode == Enum.KeyCode.E then
        print("🔧 E key pressed - Add your custom function here!")
        -- Add your custom functionality here
    elseif input.KeyCode == Enum.KeyCode.R then
        print("🔄 R key pressed - Refreshing ESP...")
        createESP()
    end
end)

-- Run ESP once on load
createESP()

-- Example: Main loop for continuous features
spawn(function()
    while wait(5) do
        -- Refresh ESP every 5 seconds
        createESP()

        -- Add other continuous features here
        print("🔄 Refreshing features...")
    end
end)

print("🎮 Just a baseplate Script Features Activated!")
print("📋 Features:")
print("   - Speed boost (50)")
print("   - Jump boost (100)")
print("   - Player ESP")
print("   - Press E for custom action")
print("   - Press R to refresh ESP")
print("✅ Script loaded via Project Madara Centralized System")`
            };

            if (gameScripts[placeId]) {
                return new Response(gameScripts[placeId], {
                    status: 200,
                    headers: { ...corsHeaders, 'Content-Type': 'text/plain' }
                });
            } else {
                return new Response('warn("Game not supported - No script found for Place ID: ' + placeId + '")', {
                    status: 404,
                    headers: { ...corsHeaders, 'Content-Type': 'text/plain' }
                });
            }
        }

        if (action === 'get_main_loader') {
            // Serve the main loader script directly (no authentication required for initial load)
            const url = new URL(request.url);
            const clearCache = url.searchParams.get('clear_cache') === 'true';
            const loaderResult = await getMainLoaderScript(clearCache);

            if (!loaderResult.success) {
                return new Response(JSON.stringify({ error: loaderResult.error }), {
                    status: 404,
                    headers: corsHeaders
                });
            }

            const content = loaderResult.script.content || '-- Main loader not available';

            return new Response(content, {
                status: 200,
                headers: {
                    ...corsHeaders,
                    'Content-Type': 'text/plain',
                    'Cache-Control': 'public, max-age=300', // Cache for 5 minutes
                    'X-Content-Size': content.length.toString()
                }
            });

        } else if (action === 'load_game_script') {
            // Load game-specific script by PlaceId (requires authentication)
            if (!keyCode || !placeId) {
                await logScriptAccess(null, keyCode, 'load_game_script', ipHash, userAgent, keyCode, false, 'Missing key or PlaceId');
                return new Response(JSON.stringify({ error: 'Key code and PlaceId required' }), {
                    status: 400,
                    headers: corsHeaders
                });
            }

            // Validate license key
            const keyValidation = await validateLicenseKey(keyCode, hwid, ipHash);
            if (!keyValidation.valid) {
                await logScriptAccess(null, keyCode, 'load_game_script', ipHash, userAgent, keyCode, false, keyValidation.error);
                return new Response(JSON.stringify({ error: keyValidation.error }), {
                    status: 403,
                    headers: corsHeaders
                });
            }

            // Get game script by PlaceId
            const gameScriptResult = await getGameScriptByPlaceId(placeId);

            if (!gameScriptResult.success) {
                await logScriptAccess(null, keyCode, 'load_game_script', ipHash, userAgent, keyCode, false, gameScriptResult.error);
                return new Response(JSON.stringify({ error: gameScriptResult.error }), {
                    status: 404,
                    headers: corsHeaders
                });
            }

            const script = gameScriptResult.script;

            // Log successful access
            await logScriptAccess(script.id, keyCode, 'load_game_script', ipHash, userAgent, keyCode, true, null, script.content?.length || 0);

            // Return script content directly or generate token for large scripts
            const content = script.content || '-- Game script not available';

            if (content.length > 50000) { // For very large scripts, use token system
                const token = generateAccessToken();
                const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes for game scripts

                await supabase
                    .from('script_access_tokens')
                    .insert({
                        token,
                        script_id: script.id,
                        user_identifier: keyCode,
                        key_code: keyCode,
                        place_id: placeId,
                        expires_at: expiresAt.toISOString(),
                        max_uses: 1,
                        ip_hash: ipHash,
                        user_agent_hash: crypto.createHash('sha256').update(userAgent || '').digest('hex')
                    });

                const baseUrl = process.env.URL || 'https://projectmadara.com';
                return new Response(JSON.stringify({
                    script_url: `${baseUrl}/.netlify/functions/secure-script?token=${token}`,
                    expires_in: 600
                }), {
                    status: 200,
                    headers: corsHeaders
                });
            } else {
                // Return content directly for smaller scripts
                return new Response(content, {
                    status: 200,
                    headers: {
                        ...corsHeaders,
                        'Content-Type': 'text/plain',
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'X-Content-Size': content.length.toString()
                    }
                });
            }

        } else if (action === 'generate_loadstring') {
            // Generate secure loadstring for authenticated users
            if (!keyCode || !scriptId) {
                await logScriptAccess(scriptId, keyCode, 'copy_loadstring', ipHash, userAgent, keyCode, false, 'Missing key or script ID');
                return new Response(JSON.stringify({ error: 'Key code and script ID required' }), {
                    status: 400,
                    headers: corsHeaders
                });
            }
            
            // Validate license key
            const keyValidation = await validateLicenseKey(keyCode, hwid, ipHash);
            if (!keyValidation.valid) {
                await logScriptAccess(scriptId, keyCode, 'copy_loadstring', ipHash, userAgent, keyCode, false, keyValidation.error);
                return new Response(JSON.stringify({ error: keyValidation.error }), {
                    status: 403,
                    headers: corsHeaders
                });
            }
            
            // Check if script exists and user has access
            const { data: script, error: scriptError } = await supabase
                .from('scripts')
                .select('*')
                .eq('id', scriptId)
                .eq('is_active', true)
                .single();
                
            if (scriptError || !script) {
                await logScriptAccess(scriptId, keyCode, 'copy_loadstring', ipHash, userAgent, keyCode, false, 'Script not found');
                return new Response(JSON.stringify({ error: 'Script not found' }), {
                    status: 404,
                    headers: corsHeaders
                });
            }
            
            // Generate secure loadstring
            const secureLoadstring = await generateSecureLoadstring(scriptId, keyCode, ipHash, userAgent);
            
            await logScriptAccess(scriptId, keyCode, 'copy_loadstring', ipHash, userAgent, keyCode, true);
            
            return new Response(JSON.stringify({ 
                loadstring: secureLoadstring,
                expires_in: 1800 // 30 minutes
            }), {
                status: 200,
                headers: corsHeaders
            });
            
        } else if (token) {
            // Serve script content using access token
            const { data: accessToken, error: tokenError } = await supabase
                .from('script_access_tokens')
                .select('*')
                .eq('token', token)
                .eq('is_revoked', false)
                .single();
                
            if (tokenError || !accessToken) {
                return new Response('-- Access denied: Invalid token', {
                    status: 403,
                    headers: { ...corsHeaders, 'Content-Type': 'text/plain' }
                });
            }
            
            // Check token expiration
            if (new Date(accessToken.expires_at) < new Date()) {
                return new Response('-- Access denied: Token expired', {
                    status: 403,
                    headers: { ...corsHeaders, 'Content-Type': 'text/plain' }
                });
            }
            
            // Check usage limits
            if (accessToken.current_uses >= accessToken.max_uses) {
                return new Response('-- Access denied: Token usage limit exceeded', {
                    status: 403,
                    headers: { ...corsHeaders, 'Content-Type': 'text/plain' }
                });
            }
            
            // Verify IP and user agent (optional security check)
            const currentUserAgentHash = crypto.createHash('sha256').update(userAgent).digest('hex');
            if (accessToken.user_agent_hash && accessToken.user_agent_hash !== currentUserAgentHash) {
                return new Response('-- Access denied: Security check failed', {
                    status: 403,
                    headers: { ...corsHeaders, 'Content-Type': 'text/plain' }
                });
            }
            
            // Try to get script from cache first
            let script = getCachedScript(accessToken.script_id);

            if (!script) {
                // Get script content from database
                const { data: scriptData, error: scriptError } = await supabase
                    .from('scripts')
                    .select('*')
                    .eq('id', accessToken.script_id)
                    .eq('is_active', true)
                    .single();

                if (scriptError || !scriptData) {
                    return new Response('-- Script not found or inactive', {
                        status: 404,
                        headers: { ...corsHeaders, 'Content-Type': 'text/plain' }
                    });
                }

                script = scriptData;
                setCachedScript(accessToken.script_id, script);
            }
            
            // Update token usage
            await supabase
                .from('script_access_tokens')
                .update({ 
                    current_uses: accessToken.current_uses + 1,
                    last_used_at: new Date().toISOString()
                })
                .eq('id', accessToken.id);
            
            // Log access
            await logScriptAccess(
                script.id, 
                accessToken.user_identifier, 
                'download_content', 
                ipHash, 
                userAgent, 
                accessToken.key_code, 
                true, 
                null, 
                script.content?.length || 0
            );
            
            // Enhanced script delivery with encryption and obfuscation
            let content = script.content || '-- Script content not available';

            // Check if script is stored encrypted
            if (script.is_encrypted && script.encrypted_content) {
                try {
                    content = decryptContent(script.encrypted_content);
                } catch (decryptError) {
                    console.error('Script decryption failed:', decryptError);
                    return new Response('-- Script decryption failed', {
                        status: 500,
                        headers: { ...corsHeaders, 'Content-Type': 'text/plain' }
                    });
                }
            }

            // Apply obfuscation for additional protection
            const obfuscatedContent = obfuscateScript(content);

            // Add runtime security wrapper
            const secureWrapper = `
-- Project Madara Secure Script Loader
-- Security Level: Enhanced
-- Token: ${token.substring(0, 8)}...

local function executeSecureScript()
    local success, error = pcall(function()
        -- Verify execution environment
        if not game or not game.GetService then
            error("Invalid execution environment")
        end

        -- Anti-tampering check
        local HttpService = game:GetService("HttpService")
        if not HttpService then
            error("HttpService not available")
        end

        -- Execute protected script
        ${obfuscatedContent}
    end)

    if not success then
        warn("Script execution failed: " .. tostring(error))
    end
end

-- Execute with protection
executeSecureScript()
`;

            const finalContent = secureWrapper;
            const acceptEncoding = request.headers.get('accept-encoding') || '';

            // Check if client supports gzip compression
            if (acceptEncoding.includes('gzip') && finalContent.length > 1000) {
                try {
                    const compressed = await compressContent(finalContent);
                    return new Response(compressed, {
                        status: 200,
                        headers: {
                            ...corsHeaders,
                            'Content-Type': 'text/plain',
                            'Content-Encoding': 'gzip',
                            'Cache-Control': 'no-cache, no-store, must-revalidate',
                            'Pragma': 'no-cache',
                            'Expires': '0',
                            'X-Original-Size': finalContent.length.toString(),
                            'X-Compressed-Size': compressed.length.toString(),
                            'X-Security-Level': 'Enhanced',
                            'X-Protection': 'Encrypted+Obfuscated'
                        }
                    });
                } catch (compressionError) {
                    console.error('Compression failed:', compressionError);
                    // Fall back to uncompressed content
                }
            }

            return new Response(finalContent, {
                status: 200,
                headers: {
                    ...corsHeaders,
                    'Content-Type': 'text/plain',
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0',
                    'X-Content-Size': finalContent.length.toString(),
                    'X-Security-Level': 'Enhanced',
                    'X-Protection': 'Encrypted+Obfuscated'
                }
            });
            
        } else {
            return new Response(JSON.stringify({ error: 'Invalid request' }), {
                status: 400,
                headers: corsHeaders
            });
        }
        
    } catch (error) {
        console.error('Secure script error:', error);
        await logScriptAccess(scriptId, keyCode, action, ipHash, userAgent, keyCode, false, error.message);
        
        return new Response(JSON.stringify({ error: 'Internal server error' }), {
            status: 500,
            headers: corsHeaders
        });
    }
};

export default allowCors(handler);
