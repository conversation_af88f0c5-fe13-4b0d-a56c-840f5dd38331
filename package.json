{"name": "project_madara", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "npm run deploy-scripts && vite build", "deploy-scripts": "node scripts/deploy-integration.js", "test-deployment": "node scripts/test-deployment-system.js", "encrypt-scripts": "node scripts/encrypt-scripts.js", "manage-scripts": "node scripts/secure-script-manager.js", "build:analyze": "vite build && npx webpack-bundle-analyzer dist/assets/*.js", "lint": "eslint .", "preview": "vite preview", "bot:start": "node discord-bot-src/index.js", "bot:dev": "node --watch discord-bot-src/index.js", "bot:register-commands": "node discord-bot-src/scripts/registerCommands.js allowed", "bot:register-global": "node discord-bot-src/scripts/registerCommands.js global", "bot:register-guild": "node discord-bot-src/scripts/registerCommands.js guild", "bot:clear-commands": "node discord-bot-src/scripts/registerCommands.js clear-global", "bot:list-commands": "node discord-bot-src/scripts/registerCommands.js list-global"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@fingerprintjs/fingerprintjs": "^4.6.2", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/themes": "^3.2.1", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.50.2", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.10", "@tanstack/react-query": "^5.81.5", "antd": "^5.26.2", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "crypto-hash": "^3.1.0", "crypto-js": "^4.2.0", "csurf": "^1.11.0", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "express-rate-limit": "^7.2.0", "express-slow-down": "^2.0.0", "framer-motion": "^12.19.2", "helmet": "^8.1.0", "hpp": "^0.2.3", "http-errors": "^2.0.0", "lucide-react": "^0.525.0", "pacote": "^21.0.0", "rate-limit-redis": "^4.1.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.59.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-is": "^19.1.0", "react-router-dom": "^7.6.3", "recharts": "^3.0.2", "sonner": "^2.0.6", "tailwind-merge": "^1.14.0", "tailwindcss": "^3.4.1", "uuid": "^11.1.0", "winston": "^3.13.0", "xss-clean": "^0.1.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "netlify-cli": "^22.2.1", "postcss-import": "^16.1.1", "postcss-nesting": "^13.0.2", "terser": "^5.43.1", "vite": "^7.0.0", "vite-plugin-cjs-interop": "^2.2.0", "webpack-bundle-analyzer": "^4.10.2"}}