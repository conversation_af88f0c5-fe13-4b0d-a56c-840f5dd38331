import { createClient } from '@supabase/supabase-js';
import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Enhanced encryption configuration
const ENCRYPTION_KEY = process.env.SCRIPT_ENCRYPTION_KEY || crypto.randomBytes(32);
const ENCRYPTION_ALGORITHM = 'aes-256-gcm';
const IV_LENGTH = 16;
const TAG_LENGTH = 16;
const SALT_LENGTH = 32;

// Enhanced encryption utilities
const encryptContent = (content) => {
    try {
        const iv = crypto.randomBytes(IV_LENGTH);
        const salt = crypto.randomBytes(SALT_LENGTH);
        const key = crypto.pbkdf2Sync(ENCRYPTION_KEY, salt, 100000, 32, 'sha512');
        
        const cipher = crypto.createCipherGCM(ENCRYPTION_ALGORITHM, key, iv);
        cipher.setAAD(Buffer.from('script-content'));

        let encrypted = cipher.update(content, 'utf8');
        encrypted = Buffer.concat([encrypted, cipher.final()]);

        const tag = cipher.getAuthTag();
        
        return {
            encrypted: Buffer.concat([salt, iv, tag, encrypted]).toString('base64'),
            hash: crypto.createHash('sha256').update(content).digest('hex')
        };
    } catch (error) {
        throw new Error('Encryption failed: ' + error.message);
    }
};

// Game name detection from PlaceID
const getGameNameFromPlaceId = (placeId) => {
    const knownGames = {
        '17574618959': 'Just a baseplate',
        '142823291': 'Murder Mystery 2',
        '537413528': 'Build A Boat For Treasure',
        '6284583030': 'Pet Simulator X',
        '155615604': 'Prison Life',
        '1537690962': 'Bee Swarm Simulator',
        '17574618959': 'Just a baseplate',
        '14958096162': 'Evolve'
    };
    
    return knownGames[placeId] || `Game ${placeId}`;
};

// Scan Roblox_Script directory for .lua files
const scanScriptDirectory = () => {
    const scriptsDir = path.join(__dirname, '..', 'Roblox_Script');
    
    if (!fs.existsSync(scriptsDir)) {
        console.log('📁 Creating Roblox_Script directory...');
        fs.mkdirSync(scriptsDir, { recursive: true });
        return [];
    }
    
    const files = fs.readdirSync(scriptsDir);
    const scriptFiles = files.filter(file => file.endsWith('.lua') && file !== 'README.md');
    
    return scriptFiles.map(file => {
        const placeId = path.basename(file, '.lua');
        const filePath = path.join(scriptsDir, file);
        const content = fs.readFileSync(filePath, 'utf8');
        
        return {
            placeId,
            fileName: file,
            filePath,
            content,
            gameName: getGameNameFromPlaceId(placeId)
        };
    });
};

// Deploy script to database
const deployScript = async (scriptInfo) => {
    try {
        console.log(`🚀 Deploying script for ${scriptInfo.gameName} (PlaceID: ${scriptInfo.placeId})`);
        
        // Encrypt the script content
        const encryptionResult = encryptContent(scriptInfo.content);
        
        // Check if script already exists
        const { data: existing, error: checkError } = await supabase
            .from('scripts')
            .select('id, version')
            .eq('place_id', scriptInfo.placeId)
            .eq('script_type', 'game_specific')
            .single();
            
        if (checkError && checkError.code !== 'PGRST116') {
            throw new Error(`Database check failed: ${checkError.message}`);
        }
        
        const scriptData = {
            name: `${scriptInfo.gameName} Script`,
            description: `Auto-deployed script for ${scriptInfo.gameName} (PlaceID: ${scriptInfo.placeId})`,
            content: scriptInfo.content, // Keep original for now
            encrypted_content: encryptionResult.encrypted,
            content_hash: encryptionResult.hash,
            is_encrypted: true,
            encryption_algorithm: ENCRYPTION_ALGORITHM,
            encrypted_at: new Date().toISOString(),
            script_type: 'game_specific',
            place_id: parseInt(scriptInfo.placeId),
            game_name: scriptInfo.gameName,
            storage_type: 'database',
            access_level: 'authenticated',
            requires_valid_key: true,
            category: 'game',
            tags: ['Game Specific', scriptInfo.gameName, 'Auto Deployed', 'Encrypted'],
            executor: 'All Executors',
            version: existing ? `${parseFloat(existing.version || '1.0') + 0.1}` : '1.0.0',
            file_size_bytes: Buffer.byteLength(scriptInfo.content, 'utf8'),
            uploaded_by: 'auto-deploy',
            is_active: true,
            last_updated: new Date().toISOString()
        };
        
        let scriptResult;
        if (existing) {
            // Update existing script
            const { data, error } = await supabase
                .from('scripts')
                .update(scriptData)
                .eq('id', existing.id)
                .select()
                .single();
                
            if (error) throw new Error(`Update failed: ${error.message}`);
            scriptResult = data;
            console.log(`✅ Updated existing script for ${scriptInfo.gameName}`);
        } else {
            // Insert new script
            const { data, error } = await supabase
                .from('scripts')
                .insert(scriptData)
                .select()
                .single();
                
            if (error) throw new Error(`Insert failed: ${error.message}`);
            scriptResult = data;
            console.log(`✅ Created new script for ${scriptInfo.gameName}`);
        }
        
        // Update game registry
        const { error: registryError } = await supabase
            .from('game_registry')
            .upsert({
                place_id: parseInt(scriptInfo.placeId),
                game_name: scriptInfo.gameName,
                game_description: `Auto-deployed support for ${scriptInfo.gameName}`,
                script_id: scriptResult.id,
                is_supported: true,
                support_level: 'full',
                genre: 'Auto-Detected',
                last_script_update: new Date().toISOString(),
                auto_deployed: true
            });
            
        if (registryError) {
            console.warn(`⚠️  Registry update failed for ${scriptInfo.gameName}: ${registryError.message}`);
        } else {
            console.log(`📋 Updated game registry for ${scriptInfo.gameName}`);
        }
        
        return {
            success: true,
            placeId: scriptInfo.placeId,
            gameName: scriptInfo.gameName,
            scriptId: scriptResult.id,
            version: scriptResult.version,
            action: existing ? 'updated' : 'created'
        };
        
    } catch (error) {
        console.error(`❌ Failed to deploy ${scriptInfo.gameName}: ${error.message}`);
        return {
            success: false,
            placeId: scriptInfo.placeId,
            gameName: scriptInfo.gameName,
            error: error.message
        };
    }
};

// Main deployment function
const autoDeployScripts = async () => {
    console.log('🚀 Project Madara Auto-Deploy System');
    console.log('====================================\n');
    
    // Scan for script files
    console.log('📁 Scanning Roblox_Script directory...');
    const scriptFiles = scanScriptDirectory();
    
    if (scriptFiles.length === 0) {
        console.log('📝 No .lua files found in Roblox_Script directory');
        console.log('💡 Add your PlaceID-named scripts (e.g., 18441747385.lua) to get started!');
        return;
    }
    
    console.log(`📝 Found ${scriptFiles.length} script file(s) to deploy:`);
    scriptFiles.forEach(script => {
        console.log(`   - ${script.fileName} (${script.gameName})`);
    });
    console.log('');
    
    // Deploy each script
    const results = [];
    for (const scriptInfo of scriptFiles) {
        const result = await deployScript(scriptInfo);
        results.push(result);
    }
    
    // Generate deployment report
    console.log('\n📊 Deployment Report');
    console.log('====================');
    
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    
    console.log(`✅ Successful: ${successful.length}`);
    console.log(`❌ Failed: ${failed.length}`);
    console.log(`📊 Total: ${results.length}\n`);
    
    if (successful.length > 0) {
        console.log('✅ Successfully Deployed:');
        successful.forEach(result => {
            console.log(`   - ${result.gameName} (PlaceID: ${result.placeId}) - ${result.action} v${result.version}`);
        });
        console.log('');
    }
    
    if (failed.length > 0) {
        console.log('❌ Failed Deployments:');
        failed.forEach(result => {
            console.log(`   - ${result.gameName} (PlaceID: ${result.placeId}): ${result.error}`);
        });
        console.log('');
    }
    
    // Update supported games list
    const supportedGames = successful.map(r => ({
        placeId: r.placeId,
        gameName: r.gameName
    }));
    
    if (supportedGames.length > 0) {
        console.log('🎮 Supported Games Updated:');
        supportedGames.forEach(game => {
            console.log(`   - ${game.gameName} (PlaceID: ${game.placeId})`);
        });
    }
    
    console.log('\n🎉 Auto-deployment completed!');
    console.log('🔗 Scripts are now available through the secure loader system');
};

// Run auto-deployment
autoDeployScripts().catch(console.error);
