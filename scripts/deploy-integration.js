import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

const log = (message, color = 'reset') => {
    console.log(`${colors[color]}${message}${colors.reset}`);
};

// Check if we're in a Netlify build environment
const isNetlifyBuild = process.env.NETLIFY === 'true';
const isProduction = process.env.NODE_ENV === 'production' || process.env.CONTEXT === 'production';

const deployIntegration = async () => {
    log('🚀 Project Madara Deploy Integration', 'cyan');
    log('=====================================', 'cyan');
    
    if (isNetlifyBuild) {
        log('📡 Netlify build environment detected', 'blue');
    }
    
    if (isProduction) {
        log('🏭 Production deployment detected', 'green');
    }
    
    // Step 1: Check for Roblox_Script directory
    const scriptsDir = path.join(__dirname, '..', 'Roblox_Script');
    
    if (!fs.existsSync(scriptsDir)) {
        log('📁 Roblox_Script directory not found, creating...', 'yellow');
        fs.mkdirSync(scriptsDir, { recursive: true });
        
        // Create README if it doesn't exist
        const readmePath = path.join(scriptsDir, 'README.md');
        if (!fs.existsSync(readmePath)) {
            const readmeContent = `# Roblox Scripts Directory

Add your PlaceID-named scripts here (e.g., 18441747385.lua) for automatic deployment.

## How it works:
1. Add your .lua files named with PlaceID
2. Run \`netlify deploy --prod\` or push to main branch
3. Scripts are automatically encrypted and deployed to secure database
4. Users can access scripts through the enhanced loader system

## Example:
- \`18441747385.lua\` - Script for PlaceID 18441747385
- \`142823291.lua\` - Script for Murder Mystery 2
`;
            fs.writeFileSync(readmePath, readmeContent);
            log('📝 Created README.md in Roblox_Script directory', 'green');
        }
        
        return;
    }
    
    // Step 2: Check for script files
    const files = fs.readdirSync(scriptsDir);
    const scriptFiles = files.filter(file => file.endsWith('.lua') && file !== 'README.md');
    
    if (scriptFiles.length === 0) {
        log('📝 No .lua files found in Roblox_Script directory', 'yellow');
        log('💡 Add your PlaceID-named scripts to enable automatic deployment', 'yellow');
        return;
    }
    
    log(`📝 Found ${scriptFiles.length} script file(s) for deployment:`, 'blue');
    scriptFiles.forEach(file => {
        const placeId = path.basename(file, '.lua');
        log(`   - ${file} (PlaceID: ${placeId})`, 'blue');
    });
    
    // Step 3: Check environment variables
    const requiredEnvVars = ['SUPABASE_URL', 'SUPABASE_SERVICE_ROLE_KEY'];
    const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
    
    if (missingEnvVars.length > 0) {
        log('❌ Missing required environment variables:', 'red');
        missingEnvVars.forEach(envVar => {
            log(`   - ${envVar}`, 'red');
        });
        
        if (isNetlifyBuild) {
            log('💡 Add these environment variables in Netlify dashboard:', 'yellow');
            log('   Site settings > Environment variables', 'yellow');
        } else {
            log('💡 Add these environment variables to your .env file', 'yellow');
        }
        
        // Don't fail the build, just skip script deployment
        log('⚠️  Skipping script deployment due to missing environment variables', 'yellow');
        return;
    }
    
    // Step 4: Run auto-deploy script
    try {
        log('🚀 Running automatic script deployment...', 'green');
        
        const deployScriptPath = path.join(__dirname, 'auto-deploy-scripts.js');
        
        if (!fs.existsSync(deployScriptPath)) {
            log('❌ Auto-deploy script not found at: ' + deployScriptPath, 'red');
            return;
        }
        
        // Execute the auto-deploy script
        const result = execSync(`node "${deployScriptPath}"`, {
            encoding: 'utf8',
            stdio: 'pipe',
            cwd: __dirname
        });
        
        log('✅ Auto-deployment completed successfully!', 'green');
        log('📊 Deployment output:', 'blue');
        console.log(result);
        
    } catch (error) {
        log('❌ Auto-deployment failed:', 'red');
        console.error(error.message);
        
        if (error.stdout) {
            log('📤 Stdout:', 'yellow');
            console.log(error.stdout);
        }
        
        if (error.stderr) {
            log('📥 Stderr:', 'yellow');
            console.error(error.stderr);
        }
        
        // Don't fail the build, just log the error
        log('⚠️  Build will continue despite deployment failure', 'yellow');
    }
    
    // Step 5: Generate deployment summary
    log('\n📋 Deployment Summary', 'cyan');
    log('====================', 'cyan');
    log(`📁 Scripts directory: ${scriptsDir}`, 'blue');
    log(`📝 Script files found: ${scriptFiles.length}`, 'blue');
    log(`🌍 Environment: ${isProduction ? 'Production' : 'Development'}`, 'blue');
    log(`📡 Netlify build: ${isNetlifyBuild ? 'Yes' : 'No'}`, 'blue');
    
    if (scriptFiles.length > 0) {
        log('\n🎮 Deployed Scripts:', 'green');
        scriptFiles.forEach(file => {
            const placeId = path.basename(file, '.lua');
            log(`   ✅ ${file} → PlaceID ${placeId}`, 'green');
        });
    }
    
    log('\n🔗 Next Steps:', 'magenta');
    log('1. Test your scripts using the enhanced loader', 'magenta');
    log('2. Monitor script access in the admin dashboard', 'magenta');
    log('3. Add more scripts by creating PlaceID-named .lua files', 'magenta');
    
    log('\n🎉 Deploy integration completed!', 'cyan');
};

// Run deployment integration
deployIntegration().catch(error => {
    log('❌ Deploy integration failed:', 'red');
    console.error(error);
    // Don't exit with error code to avoid failing the build
    process.exit(0);
});
