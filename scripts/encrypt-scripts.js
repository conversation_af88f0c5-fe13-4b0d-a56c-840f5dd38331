import { createClient } from '@supabase/supabase-js';
import crypto from 'crypto';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Enhanced encryption configuration
const ENCRYPTION_KEY = process.env.SCRIPT_ENCRYPTION_KEY || crypto.randomBytes(32);
const ENCRYPTION_ALGORITHM = 'aes-256-gcm';
const IV_LENGTH = 16;
const TAG_LENGTH = 16;
const SALT_LENGTH = 32;

// Enhanced encryption utilities
const encryptContent = (content) => {
    try {
        const iv = crypto.randomBytes(IV_LENGTH);
        const salt = crypto.randomBytes(SALT_LENGTH);
        const key = crypto.pbkdf2Sync(ENCRYPTION_KEY, salt, 100000, 32, 'sha512');
        
        const cipher = crypto.createCipher(ENCRYPTION_ALGORITHM, key);
        cipher.setAAD(Buffer.from('script-content'));
        
        let encrypted = cipher.update(content, 'utf8');
        encrypted = Buffer.concat([encrypted, cipher.final()]);
        
        const tag = cipher.getAuthTag();
        
        return {
            encrypted: Buffer.concat([salt, iv, tag, encrypted]).toString('base64'),
            hash: crypto.createHash('sha256').update(content).digest('hex')
        };
    } catch (error) {
        throw new Error('Encryption failed: ' + error.message);
    }
};

async function encryptExistingScripts() {
    console.log('🔐 Starting script encryption process...');
    
    try {
        // Get all unencrypted scripts
        const { data: scripts, error } = await supabase
            .from('scripts')
            .select('*')
            .is('encrypted_content', null)
            .not('content', 'is', null);
            
        if (error) {
            throw new Error('Failed to fetch scripts: ' + error.message);
        }
        
        if (!scripts || scripts.length === 0) {
            console.log('✅ No scripts found that need encryption');
            return;
        }
        
        console.log(`📝 Found ${scripts.length} scripts to encrypt`);
        
        for (const script of scripts) {
            try {
                console.log(`🔒 Encrypting script: ${script.name} (ID: ${script.id})`);
                
                // Encrypt the script content
                const encryptionResult = encryptContent(script.content);
                
                // Update the script with encrypted content
                const { error: updateError } = await supabase
                    .from('scripts')
                    .update({
                        encrypted_content: encryptionResult.encrypted,
                        content_hash: encryptionResult.hash,
                        is_encrypted: true,
                        encryption_algorithm: ENCRYPTION_ALGORITHM,
                        encrypted_at: new Date().toISOString(),
                        // Keep original content for now (can be removed later for security)
                        // content: script.content
                    })
                    .eq('id', script.id);
                    
                if (updateError) {
                    console.error(`❌ Failed to encrypt script ${script.name}:`, updateError);
                    continue;
                }
                
                console.log(`✅ Successfully encrypted script: ${script.name}`);
                
            } catch (scriptError) {
                console.error(`❌ Error encrypting script ${script.name}:`, scriptError);
            }
        }
        
        console.log('\n🎉 Script encryption process completed!');
        
        // Add database schema updates if needed
        console.log('\n📋 Database Schema Updates Needed:');
        console.log('- Add encrypted_content column (TEXT)');
        console.log('- Add is_encrypted column (BOOLEAN, default false)');
        console.log('- Add encryption_algorithm column (VARCHAR)');
        console.log('- Add encrypted_at column (TIMESTAMP)');
        console.log('- Add content_hash column (VARCHAR) for integrity verification');
        
    } catch (error) {
        console.error('❌ Script encryption failed:', error);
        process.exit(1);
    }
}

// Add database schema creation function
async function createEncryptionSchema() {
    console.log('🗄️ Creating encryption schema...');
    
    try {
        // Note: These would typically be run as SQL migrations
        const schemaUpdates = [
            'ALTER TABLE scripts ADD COLUMN IF NOT EXISTS encrypted_content TEXT;',
            'ALTER TABLE scripts ADD COLUMN IF NOT EXISTS is_encrypted BOOLEAN DEFAULT false;',
            'ALTER TABLE scripts ADD COLUMN IF NOT EXISTS encryption_algorithm VARCHAR(50);',
            'ALTER TABLE scripts ADD COLUMN IF NOT EXISTS encrypted_at TIMESTAMP;',
            'ALTER TABLE scripts ADD COLUMN IF NOT EXISTS content_hash VARCHAR(64);'
        ];
        
        console.log('📝 Schema updates needed:');
        schemaUpdates.forEach(sql => console.log(`  ${sql}`));
        
        console.log('\n⚠️  Please run these SQL commands manually in your Supabase dashboard');
        console.log('   or create a proper migration file.');
        
    } catch (error) {
        console.error('❌ Schema creation failed:', error);
    }
}

// Main execution
async function main() {
    console.log('🚀 Project Madara Script Encryption Utility');
    console.log('==========================================\n');
    
    // Check if we should create schema first
    const args = process.argv.slice(2);
    if (args.includes('--schema')) {
        await createEncryptionSchema();
        return;
    }
    
    await encryptExistingScripts();
}

main().catch(console.error);
