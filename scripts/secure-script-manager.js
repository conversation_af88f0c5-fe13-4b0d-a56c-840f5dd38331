import { createClient } from '@supabase/supabase-js';
import crypto from 'crypto';
import fs from 'fs';
import path from 'path';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Enhanced encryption configuration
const ENCRYPTION_KEY = process.env.SCRIPT_ENCRYPTION_KEY || crypto.randomBytes(32);
const ENCRYPTION_ALGORITHM = 'aes-256-gcm';
const IV_LENGTH = 16;
const TAG_LENGTH = 16;
const SALT_LENGTH = 32;

// Enhanced encryption utilities
const encryptContent = (content) => {
    try {
        const iv = crypto.randomBytes(IV_LENGTH);
        const salt = crypto.randomBytes(SALT_LENGTH);
        const key = crypto.pbkdf2Sync(ENCRYPTION_KEY, salt, 100000, 32, 'sha512');
        
        const cipher = crypto.createCipher(ENCRYPTION_ALGORITHM, key);
        cipher.setAAD(Buffer.from('script-content'));
        
        let encrypted = cipher.update(content, 'utf8');
        encrypted = Buffer.concat([encrypted, cipher.final()]);
        
        const tag = cipher.getAuthTag();
        
        return {
            encrypted: Buffer.concat([salt, iv, tag, encrypted]).toString('base64'),
            hash: crypto.createHash('sha256').update(content).digest('hex')
        };
    } catch (error) {
        throw new Error('Encryption failed: ' + error.message);
    }
};

const decryptContent = (encryptedData) => {
    try {
        const buffer = Buffer.from(encryptedData, 'base64');
        const salt = buffer.slice(0, SALT_LENGTH);
        const iv = buffer.slice(SALT_LENGTH, SALT_LENGTH + IV_LENGTH);
        const tag = buffer.slice(SALT_LENGTH + IV_LENGTH, SALT_LENGTH + IV_LENGTH + TAG_LENGTH);
        const encrypted = buffer.slice(SALT_LENGTH + IV_LENGTH + TAG_LENGTH);
        
        const key = crypto.pbkdf2Sync(ENCRYPTION_KEY, salt, 100000, 32, 'sha512');
        
        const decipher = crypto.createDecipher(ENCRYPTION_ALGORITHM, key);
        decipher.setAAD(Buffer.from('script-content'));
        decipher.setAuthTag(tag);
        
        let decrypted = decipher.update(encrypted);
        decrypted = Buffer.concat([decrypted, decipher.final()]);
        
        return decrypted.toString('utf8');
    } catch (error) {
        throw new Error('Decryption failed: ' + error.message);
    }
};

// Migrate scripts from files to secure database storage
async function migrateScriptsToSecureStorage() {
    console.log('📦 Migrating scripts to secure storage...');
    
    const scriptsToMigrate = [
        {
            file: '../Roblox_Script/14958096162.lua',
            name: 'Evolve Script',
            description: 'Advanced Evolve script with food teleportation, ESP, fly mode, and game-specific features',
            place_id: 14958096162,
            game_name: 'Evolve',
            script_type: 'game_specific'
        },
        {
            file: '../Roblox_Script/17574618959.lua', 
            name: 'Baseplate Script',
            description: 'Simple baseplate script with basic functionality and authentication confirmation',
            place_id: 17574618959,
            game_name: 'Just a baseplate',
            script_type: 'game_specific'
        }
    ];
    
    for (const scriptInfo of scriptsToMigrate) {
        try {
            const filePath = path.join(__dirname, scriptInfo.file);
            
            // Check if file exists
            if (!fs.existsSync(filePath)) {
                console.log(`⚠️  File not found: ${scriptInfo.file} (may have been already migrated)`);
                continue;
            }
            
            // Read script content
            const content = fs.readFileSync(filePath, 'utf8');
            
            // Encrypt the content
            const encryptionResult = encryptContent(content);
            
            // Check if script already exists
            const { data: existing } = await supabase
                .from('scripts')
                .select('id')
                .eq('place_id', scriptInfo.place_id)
                .eq('script_type', scriptInfo.script_type)
                .single();
                
            if (existing) {
                console.log(`✅ Script for ${scriptInfo.name} already exists in database`);
                continue;
            }
            
            // Insert encrypted script into database
            const { data: script, error } = await supabase
                .from('scripts')
                .insert({
                    name: scriptInfo.name,
                    description: scriptInfo.description,
                    content: content, // Keep original for now
                    encrypted_content: encryptionResult.encrypted,
                    content_hash: encryptionResult.hash,
                    is_encrypted: true,
                    encryption_algorithm: ENCRYPTION_ALGORITHM,
                    encrypted_at: new Date().toISOString(),
                    script_type: scriptInfo.script_type,
                    place_id: scriptInfo.place_id,
                    game_name: scriptInfo.game_name,
                    storage_type: 'database',
                    access_level: 'authenticated',
                    requires_valid_key: true,
                    category: 'game',
                    tags: ['Game Specific', scriptInfo.game_name, 'Migrated', 'Encrypted'],
                    executor: 'All Executors',
                    version: '1.0.0',
                    file_size_bytes: Buffer.byteLength(content, 'utf8'),
                    uploaded_by: 'system',
                    is_active: true
                })
                .select()
                .single();
                
            if (error) {
                console.error(`❌ Failed to migrate ${scriptInfo.name}:`, error);
                continue;
            }
            
            console.log(`✅ Successfully migrated ${scriptInfo.name} to secure storage`);
            
            // Add to game registry if needed
            const { error: registryError } = await supabase
                .from('game_registry')
                .upsert({
                    place_id: scriptInfo.place_id,
                    game_name: scriptInfo.game_name,
                    game_description: scriptInfo.description,
                    script_id: script.id,
                    is_supported: true,
                    support_level: 'full',
                    genre: 'Various',
                    last_script_update: new Date().toISOString()
                });
                
            if (registryError) {
                console.error(`⚠️  Failed to update game registry for ${scriptInfo.name}:`, registryError);
            }
            
        } catch (error) {
            console.error(`❌ Error migrating ${scriptInfo.name}:`, error);
        }
    }
    
    console.log('\n🎉 Script migration completed!');
}

// Remove original script content for security (keep only encrypted)
async function removeOriginalContent() {
    console.log('🗑️  Removing original script content for enhanced security...');
    
    try {
        const { data: scripts, error } = await supabase
            .from('scripts')
            .select('id, name')
            .eq('is_encrypted', true)
            .not('content', 'is', null);
            
        if (error) {
            throw new Error('Failed to fetch encrypted scripts: ' + error.message);
        }
        
        if (!scripts || scripts.length === 0) {
            console.log('✅ No scripts found with original content to remove');
            return;
        }
        
        console.log(`🔒 Found ${scripts.length} encrypted scripts with original content`);
        
        for (const script of scripts) {
            const { error: updateError } = await supabase
                .from('scripts')
                .update({ content: null }) // Remove original content
                .eq('id', script.id);
                
            if (updateError) {
                console.error(`❌ Failed to remove content for ${script.name}:`, updateError);
                continue;
            }
            
            console.log(`✅ Removed original content for: ${script.name}`);
        }
        
        console.log('\n🔐 Original content removal completed!');
        console.log('⚠️  Scripts are now only accessible through encrypted storage');
        
    } catch (error) {
        console.error('❌ Content removal failed:', error);
    }
}

// Main execution
async function main() {
    console.log('🔐 Project Madara Secure Script Manager');
    console.log('=====================================\n');
    
    const args = process.argv.slice(2);
    
    if (args.includes('--migrate')) {
        await migrateScriptsToSecureStorage();
    } else if (args.includes('--remove-original')) {
        await removeOriginalContent();
    } else {
        console.log('Available commands:');
        console.log('  --migrate         Migrate scripts from files to secure database storage');
        console.log('  --remove-original Remove original content from encrypted scripts');
        console.log('\nExample: node secure-script-manager.js --migrate');
    }
}

main().catch(console.error);
