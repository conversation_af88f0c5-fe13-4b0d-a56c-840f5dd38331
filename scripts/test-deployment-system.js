import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

const log = (message, color = 'reset') => {
    console.log(`${colors[color]}${message}${colors.reset}`);
};

// Test functions
const testDatabaseConnection = async () => {
    log('🔌 Testing database connection...', 'blue');
    
    try {
        const { data, error } = await supabase
            .from('scripts')
            .select('count(*)')
            .limit(1);
            
        if (error) {
            throw error;
        }
        
        log('✅ Database connection successful', 'green');
        return true;
    } catch (error) {
        log('❌ Database connection failed: ' + error.message, 'red');
        return false;
    }
};

const testScriptDirectory = () => {
    log('📁 Testing Roblox_Script directory...', 'blue');
    
    const scriptsDir = path.join(__dirname, '..', 'Roblox_Script');
    
    if (!fs.existsSync(scriptsDir)) {
        log('❌ Roblox_Script directory not found', 'red');
        return false;
    }
    
    const files = fs.readdirSync(scriptsDir);
    const scriptFiles = files.filter(file => file.endsWith('.lua') && file !== 'README.md');
    
    log(`✅ Directory found with ${scriptFiles.length} script file(s)`, 'green');
    
    if (scriptFiles.length > 0) {
        log('📝 Script files:', 'blue');
        scriptFiles.forEach(file => {
            const placeId = path.basename(file, '.lua');
            const filePath = path.join(scriptsDir, file);
            const stats = fs.statSync(filePath);
            log(`   - ${file} (PlaceID: ${placeId}, Size: ${stats.size} bytes)`, 'blue');
        });
    }
    
    return true;
};

const testScriptDeployment = async () => {
    log('🚀 Testing script deployment...', 'blue');
    
    const scriptsDir = path.join(__dirname, '..', 'Roblox_Script');
    const files = fs.readdirSync(scriptsDir);
    const scriptFiles = files.filter(file => file.endsWith('.lua') && file !== 'README.md');
    
    if (scriptFiles.length === 0) {
        log('⚠️  No script files to test deployment', 'yellow');
        return true;
    }
    
    let successCount = 0;
    let failCount = 0;
    
    for (const file of scriptFiles) {
        const placeId = path.basename(file, '.lua');
        
        try {
            // Check if script exists in database
            const { data: existing, error } = await supabase
                .from('scripts')
                .select('id, name, place_id, is_active')
                .eq('place_id', parseInt(placeId))
                .eq('script_type', 'game_specific')
                .single();
                
            if (existing && !error) {
                log(`✅ Script for PlaceID ${placeId} found in database (ID: ${existing.id})`, 'green');
                successCount++;
            } else {
                log(`❌ Script for PlaceID ${placeId} not found in database`, 'red');
                failCount++;
            }
        } catch (dbError) {
            log(`❌ Database error checking PlaceID ${placeId}: ${dbError.message}`, 'red');
            failCount++;
        }
    }
    
    log(`📊 Deployment test results: ${successCount} success, ${failCount} failed`, 
        failCount === 0 ? 'green' : 'yellow');
    
    return failCount === 0;
};

const testGameSupportAPI = async () => {
    log('🎮 Testing game support API...', 'blue');
    
    const testPlaceIds = ['18441747385', '142823291', '999999999']; // Last one should fail
    
    for (const placeId of testPlaceIds) {
        try {
            const url = `https://projectmadara.com/.netlify/functions/secure-script?action=check_game_support&place_id=${placeId}`;
            
            const response = await fetch(url);
            const result = await response.json();
            
            if (result.supported) {
                log(`✅ PlaceID ${placeId}: Supported (${result.game_name})`, 'green');
            } else {
                log(`❌ PlaceID ${placeId}: Not supported`, placeId === '999999999' ? 'yellow' : 'red');
            }
        } catch (error) {
            log(`❌ API test failed for PlaceID ${placeId}: ${error.message}`, 'red');
        }
    }
    
    return true;
};

const testKeySystemUI = async () => {
    log('🔑 Testing key system UI...', 'blue');
    
    try {
        const url = 'https://projectmadara.com/.netlify/functions/secure-script?action=get_key_system_ui';
        
        const response = await fetch(url);
        const uiScript = await response.text();
        
        if (uiScript && uiScript.includes('PROJECT MADARA KEY SYSTEM UI')) {
            log('✅ Key system UI loaded successfully', 'green');
            log(`📏 UI script size: ${uiScript.length} characters`, 'blue');
            return true;
        } else {
            log('❌ Key system UI failed to load or invalid content', 'red');
            return false;
        }
    } catch (error) {
        log('❌ Key system UI test failed: ' + error.message, 'red');
        return false;
    }
};

const testSecurityFeatures = async () => {
    log('🔒 Testing security features...', 'blue');
    
    const tests = [
        {
            name: 'Encryption functions',
            test: () => {
                // Test if encryption utilities are available
                const encryptScriptPath = path.join(__dirname, 'encrypt-scripts.js');
                return fs.existsSync(encryptScriptPath);
            }
        },
        {
            name: 'Enhanced loader',
            test: () => {
                const loaderPath = path.join(__dirname, '..', 'enhanced-main-loader.lua');
                return fs.existsSync(loaderPath);
            }
        },
        {
            name: 'Secure script endpoint',
            test: () => {
                const endpointPath = path.join(__dirname, '..', 'netlify', 'functions', 'secure-script.js');
                return fs.existsSync(endpointPath);
            }
        }
    ];
    
    let passedTests = 0;
    
    for (const test of tests) {
        try {
            const result = test.test();
            if (result) {
                log(`✅ ${test.name}: Passed`, 'green');
                passedTests++;
            } else {
                log(`❌ ${test.name}: Failed`, 'red');
            }
        } catch (error) {
            log(`❌ ${test.name}: Error - ${error.message}`, 'red');
        }
    }
    
    log(`🔒 Security tests: ${passedTests}/${tests.length} passed`, 
        passedTests === tests.length ? 'green' : 'yellow');
    
    return passedTests === tests.length;
};

const generateTestReport = (results) => {
    log('\n📋 Test Report', 'cyan');
    log('=============', 'cyan');
    
    const totalTests = Object.keys(results).length;
    const passedTests = Object.values(results).filter(result => result).length;
    
    log(`📊 Overall: ${passedTests}/${totalTests} tests passed`, 
        passedTests === totalTests ? 'green' : 'yellow');
    
    log('\n📝 Detailed Results:', 'blue');
    Object.entries(results).forEach(([testName, passed]) => {
        const status = passed ? '✅ PASS' : '❌ FAIL';
        const color = passed ? 'green' : 'red';
        log(`   ${status} ${testName}`, color);
    });
    
    if (passedTests === totalTests) {
        log('\n🎉 All tests passed! System is ready for deployment.', 'green');
    } else {
        log('\n⚠️  Some tests failed. Please review and fix issues before deployment.', 'yellow');
    }
    
    log('\n🔗 Next Steps:', 'magenta');
    log('1. Run `npm run deploy-scripts` to deploy your scripts', 'magenta');
    log('2. Test the enhanced loader with a valid license key', 'magenta');
    log('3. Monitor script access logs in the database', 'magenta');
    log('4. Deploy to production with `netlify deploy --prod`', 'magenta');
};

// Main test function
const runTests = async () => {
    log('🧪 Project Madara Deployment System Tests', 'cyan');
    log('==========================================', 'cyan');
    
    const results = {};
    
    // Run all tests
    results['Database Connection'] = await testDatabaseConnection();
    results['Script Directory'] = testScriptDirectory();
    results['Script Deployment'] = await testScriptDeployment();
    results['Game Support API'] = await testGameSupportAPI();
    results['Key System UI'] = await testKeySystemUI();
    results['Security Features'] = await testSecurityFeatures();
    
    // Generate report
    generateTestReport(results);
    
    // Exit with appropriate code
    const allPassed = Object.values(results).every(result => result);
    process.exit(allPassed ? 0 : 1);
};

// Run tests
runTests().catch(error => {
    log('❌ Test suite failed:', 'red');
    console.error(error);
    process.exit(1);
});
