-- PROJECT MADARA - ENHANCED SECURE MAIN LOADER
-- Security Level: Maximum
-- Anti-tampering, encryption, and advanced protection

-- Security initialization
local function initializeSecurity()
    -- Anti-debugging measures
    local success, result = pcall(function()
        return game:GetService("HttpService"):JSONEncode({security_check = true})
    end)
    if not success then
        error("Security initialization failed")
    end
    
    -- Environment validation
    if not game or not game.GetService then
        error("Invalid execution environment detected")
    end
    
    -- Service availability check
    local requiredServices = {"HttpService", "Players", "RunService", "UserInputService"}
    for _, serviceName in pairs(requiredServices) do
        local service = game:GetService(serviceName)
        if not service then
            error("Required service not available: " .. serviceName)
        end
    end
    
    return true
end

-- Initialize security
local securityInitialized = initializeSecurity()
if not securityInitialized then
    error("Security initialization failed")
end

-- Enhanced HWID generation with multiple fallbacks
local function getSecureHWID()
    local hwid = nil
    
    -- Primary HWID method
    local success1, result1 = pcall(function()
        return game:GetService("RbxAnalyticsService"):GetClientId()
    end)
    
    if success1 and result1 and result1 ~= "" then
        hwid = result1
    else
        -- Fallback HWID method
        local success2, result2 = pcall(function()
            local HttpService = game:GetService("HttpService")
            local Players = game:GetService("Players")
            local LocalPlayer = Players.LocalPlayer
            
            if LocalPlayer then
                local data = {
                    userId = LocalPlayer.UserId,
                    name = LocalPlayer.Name,
                    timestamp = tick()
                }
                return HttpService:GenerateGUID(false)
            end
        end)
        
        if success2 and result2 then
            hwid = result2
        else
            error("Unable to generate secure HWID")
        end
    end
    
    return hwid
end

-- Enhanced game support checking
local function isGameSupported()
    local placeId = tostring(game.PlaceId)
    
    -- Supported games with enhanced security
    local supportedGames = {
        ["17574618959"] = {name = "Just a baseplate", security_level = "high"},
        ["14958096162"] = {name = "Evolve", security_level = "maximum"},
        ["142823291"] = {name = "Murder Mystery 2", security_level = "high"},
        ["537413528"] = {name = "Build A Boat For Treasure", security_level = "medium"},
        ["6284583030"] = {name = "Pet Simulator X", security_level = "high"},
        ["155615604"] = {name = "Prison Life", security_level = "medium"},
        ["1537690962"] = {name = "Bee Swarm Simulator", security_level = "high"}
    }
    
    local gameInfo = supportedGames[placeId]
    if gameInfo then
        print("🎮 Game Detected: " .. gameInfo.name)
        print("🔒 Security Level: " .. gameInfo.security_level)
        return true, gameInfo
    end
    
    return false, nil
end

-- Enhanced key validation with multiple security checks
local function validateKey(key)
    if not key or #key == 0 then
        error("Valid key required")
    end
    
    -- Key format validation
    if not string.match(key, "^MADARA%-[A-Z0-9][A-Z0-9][A-Z0-9][A-Z0-9]%-[A-Z0-9][A-Z0-9][A-Z0-9][A-Z0-9]%-[A-Z0-9][A-Z0-9][A-Z0-9][A-Z0-9]$") then
        error("Invalid key format")
    end
    
    local hwid = getSecureHWID()
    print("🔑 Validating key: " .. key:sub(1, 15) .. "...")
    print("🆔 HWID: " .. hwid:sub(1, 8) .. "...")
    
    -- Enhanced validation URL with security headers
    local url = "https://projectmadara.com/.netlify/functions/validate-key"
    local headers = {
        ["Content-Type"] = "application/json",
        ["X-Key-Code"] = key,
        ["X-HWID"] = hwid,
        ["X-Security-Level"] = "enhanced",
        ["User-Agent"] = "ProjectMadara/SecureLoader"
    }
    
    local success, response = pcall(function()
        return game:GetService("HttpService"):RequestAsync({
            Url = url,
            Method = "POST",
            Headers = headers,
            Body = game:GetService("HttpService"):JSONEncode({
                key_code = key,
                hwid = hwid,
                place_id = tostring(game.PlaceId),
                security_check = true
            })
        })
    end)
    
    if not success then
        error("Key validation request failed")
    end
    
    if response.StatusCode ~= 200 then
        error("Key validation failed: " .. (response.StatusMessage or "Unknown error"))
    end
    
    local validationResult = game:GetService("HttpService"):JSONDecode(response.Body)
    
    if not validationResult.valid then
        error("Key validation failed: " .. (validationResult.error or "Invalid key"))
    end
    
    print("✅ Key validation successful")
    return true, validationResult
end

-- Secure script loading with enhanced protection
local function loadSecureScript()
    local placeId = tostring(game.PlaceId)
    local hwid = getSecureHWID()
    
    -- Get validated key from global scope (set by UI)
    if not _G.MADARA_VALIDATED_KEY then
        error("No validated key available")
    end
    
    local key = _G.MADARA_VALIDATED_KEY
    
    print("🎮 Loading secure script for Place ID: " .. placeId)
    
    -- Enhanced script loading with security token
    local url = "https://projectmadara.com/.netlify/functions/secure-script"
    local headers = {
        ["Content-Type"] = "application/json",
        ["X-Key-Code"] = key,
        ["X-HWID"] = hwid,
        ["X-Security-Level"] = "maximum",
        ["User-Agent"] = "ProjectMadara/SecureLoader"
    }
    
    local requestBody = {
        action = "load_game_script",
        place_id = placeId,
        key_code = key,
        hwid = hwid,
        security_level = "enhanced"
    }
    
    local success, response = pcall(function()
        return game:GetService("HttpService"):RequestAsync({
            Url = url,
            Method = "POST",
            Headers = headers,
            Body = game:GetService("HttpService"):JSONEncode(requestBody)
        })
    end)
    
    if not success then
        error("Script loading request failed")
    end
    
    if response.StatusCode ~= 200 then
        error("Script loading failed: " .. (response.StatusMessage or "Unknown error"))
    end
    
    -- Handle different response types
    local contentType = response.Headers["content-type"] or ""
    
    if string.find(contentType, "application/json") then
        -- JSON response with script URL or error
        local result = game:GetService("HttpService"):JSONDecode(response.Body)
        
        if result.error then
            error("Script loading failed: " .. result.error)
        end
        
        if result.script_url then
            -- Load script from secure URL
            local scriptContent = game:HttpGet(result.script_url)
            local loadSuccess, loadError = pcall(loadstring(scriptContent))
            
            if loadSuccess then
                print("✅ Secure script loaded successfully!")
            else
                error("Script execution failed: " .. tostring(loadError))
            end
        end
    else
        -- Direct script content
        local scriptContent = response.Body
        
        if scriptContent and scriptContent ~= "" and not string.find(scriptContent, "error") then
            local loadSuccess, loadError = pcall(loadstring(scriptContent))
            
            if loadSuccess then
                print("✅ Secure script loaded successfully!")
            else
                error("Script execution failed: " .. tostring(loadError))
            end
        else
            error("No valid script content received")
        end
    end
end

-- Anti-tampering protection
local function startAntiTamperingProtection()
    spawn(function()
        while wait(math.random(30, 60)) do
            local success = pcall(initializeSecurity)
            if not success then
                error("Anti-tampering check failed")
            end
        end
    end)
end

-- Main execution function
local function executeSecureLoader()
    print("🚀 Project Madara Enhanced Secure Loader")
    print("🔒 Security Level: Maximum")
    print("==========================================")
    
    -- Check game support
    local supported, gameInfo = isGameSupported()
    if not supported then
        error("Game not supported - Place ID: " .. tostring(game.PlaceId))
    end
    
    -- Start anti-tampering protection
    startAntiTamperingProtection()
    
    print("✅ Security initialization complete")
    print("🎮 Game: " .. gameInfo.name)
    print("🔒 Security Level: " .. gameInfo.security_level)
    print("📍 Place ID: " .. tostring(game.PlaceId))
    print("\n🔑 Please enter your license key to continue...")
    
    -- The UI will handle key input and validation
    -- Once validated, it will call loadSecureScript()
end

-- Export functions for UI integration
_G.MADARA_SECURITY = {
    validateKey = validateKey,
    loadSecureScript = loadSecureScript,
    isGameSupported = isGameSupported,
    getSecureHWID = getSecureHWID
}

-- Execute the secure loader
executeSecureLoader()
